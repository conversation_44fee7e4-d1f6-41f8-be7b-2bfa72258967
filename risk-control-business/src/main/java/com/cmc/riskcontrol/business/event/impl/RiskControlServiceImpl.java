package com.cmc.riskcontrol.business.event.impl;

import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.riskcontrol.business.converter.EventRecordDtoConverter;
import com.cmc.riskcontrol.business.event.RiskControlService;
import com.cmc.riskcontrol.business.integration.BinanceCommonServiceClient;
import com.cmc.riskcontrol.business.message.EventMessageSendService;
import com.cmc.riskcontrol.model.constant.MetricConstant;
import com.cmc.riskcontrol.model.dto.EventJudgeRequestDto;
import com.cmc.riskcontrol.model.dto.EventJudgeResponseDto;
import com.cmc.riskcontrol.model.dto.EventRecordRequestDto;
import com.cmc.riskcontrol.model.enums.EventEnum;
import com.cmc.riskcontrol.model.kafka.EventMessage;
import com.cmc.riskcontrol.model.kafka.PushMessageEvent;
import com.cmc.riskcontrol.model.rmi.CommonRuleRequestDto;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2022/2/8 10:58
 */
@Service
@Slf4j
public class RiskControlServiceImpl implements RiskControlService {

    @Value("${com.cmc.riskcontrol.event.record.topic:}")
    private String eventRecordTopic;

    private final BinanceCommonServiceClient binanceCommonServiceClient;

    private final EventMessageSendService eventMessageSendService;

    private final EventRecordDtoConverter eventRecordDtoConverter;

    private final CmcMeterRegistry cmcMeterRegistry;

    public RiskControlServiceImpl(BinanceCommonServiceClient binanceCommonServiceClient, EventMessageSendService eventMessageSendService,
        EventRecordDtoConverter eventRecordDtoConverter, CmcMeterRegistry cmcMeterRegistry) {
        this.binanceCommonServiceClient = binanceCommonServiceClient;
        this.eventMessageSendService = eventMessageSendService;
        this.eventRecordDtoConverter = eventRecordDtoConverter;
        this.cmcMeterRegistry = cmcMeterRegistry;
    }

    @Override
    public Mono<Boolean> record(EventRecordRequestDto eventRecordRequestDto) {
        EventMessage eventMessage = eventRecordDtoConverter.convert(eventRecordRequestDto);
        PushMessageEvent messageEvent = PushMessageEvent.builder().kafkaTopicName(eventRecordTopic).message(JacksonUtils.serialize(eventMessage)).build();
        return eventMessageSendService.send(messageEvent);
    }

    @Override
    public Mono<EventJudgeResponseDto> judge(EventJudgeRequestDto eventJudgeRequestDto) {
        EventEnum event = EventEnum.findByCode(eventJudgeRequestDto.getEventCode());
        if (event == null) {
            return Mono.empty();
        }
        CommonRuleRequestDto dto = new CommonRuleRequestDto();
        CommonRuleRequestDto.CommonRuleBody body = new CommonRuleRequestDto.CommonRuleBody();
        body.setContext(eventJudgeRequestDto.getContext());
        body.setEventCode(event.getValue());
        dto.setBody(body);

        return binanceCommonServiceClient.commonRule(dto).map(res -> {
            EventJudgeResponseDto eventJudgeResponseDto = new EventJudgeResponseDto();
            eventJudgeResponseDto.setIsHit(Optional.ofNullable(res.getIsHit()).orElse(false));

            if (res.getExtend() != null && res.getExtend().containsKey("riskContent")) {
                Object riskContent = res.getExtend().get("riskContent");
                eventJudgeResponseDto.setRiskContent(String.valueOf(riskContent));
            }

            if (res.getIsHit() != null && res.getIsHit()) {
                cmcMeterRegistry.counter(MetricConstant.RISK_CONTROL_API_COUNT, "api", "judge", "hit").increment();
                log.info("event {} hit info {}", event.getValue(), res.getHitRules());
            }
            return eventJudgeResponseDto;
        });
    }

}
