package com.cmc.riskcontrol.business.utils;

import com.cmc.framework.utils.DatetimeUtils;
import java.time.ZonedDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/02/16
 * @description
 **/
public class DateTimeUtils extends DatetimeUtils {
    /**
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String transferUTCTimeAndFormat(Date date, LocalDateFormatEnum pattern) {
        ZonedDateTime zonedDateTime = date.toInstant().atZone(UTC);
        return pattern.getDateFormatter().format(zonedDateTime);
    }
}
