package com.cmc.riskcontrol.business.url.impl;

import static com.cmc.riskcontrol.model.constant.UrlValidationConstants.BLACKLIST_PATTERNS;

import com.cmc.framework.utils.CollectionUtils;
import com.cmc.riskcontrol.business.url.CustomSsrfPatternValidator;
import com.cmc.riskcontrol.business.url.UrlService;
import com.cmc.riskcontrol.model.dto.UrlValidationRequestDto;
import com.cmc.riskcontrol.model.dto.UrlValidationResponseDto;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/7/28 10:28
 */
@Service
public class UrlServiceImpl implements UrlService {

    @Autowired
    private CustomSsrfPatternValidator customSsrfPatternValidator;

    @Override
    public Mono<UrlValidationResponseDto> validateUrl(UrlValidationRequestDto request) {
        return Mono.fromSupplier(() -> doValidateUrl(request.getUrl()));
    }

    private UrlValidationResponseDto doValidateUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return new UrlValidationResponseDto(false, "URL cannot be empty");
        }

        List<String> matchedPatterns = getMatchedPatterns(url);
        if (!matchedPatterns.isEmpty()) {
            return new UrlValidationResponseDto(false, "URL contains potential SSRF attack patterns: " + String.join(", ", matchedPatterns));
        }

        return new UrlValidationResponseDto(true, "URL validation passed");
    }

    /**
     * Retrieves the list of SSRF attack patterns matched within the given URL.
     *
     * @param url The URL to be checked.
     * @return A list of matched attack patterns.
     */
    public List<String> getMatchedPatterns(String url) {
        List<String> matches = new ArrayList<>();

        if (url == null || url.trim().isEmpty()) {
            return matches;
        }

        List<Pattern> patterns = new ArrayList<>(Arrays.asList(BLACKLIST_PATTERNS));
        List<Pattern> customPatterns = customSsrfPatternValidator.getCompiledPatterns();
        if (CollectionUtils.isNotEmpty(customPatterns)) {
            patterns.addAll(customPatterns);
        }

        for (Pattern pattern : patterns) {
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                matches.add(pattern.pattern());
            }
        }

        return matches;
    }
}
