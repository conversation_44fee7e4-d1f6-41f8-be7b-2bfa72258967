package com.cmc.riskcontrol.business.message.impl;

import com.cmc.email.sdk.producer.EmailContentVO;
import com.cmc.email.sdk.producer.EmailSender;
import com.cmc.framework.utils.DatetimeUtils;
import com.cmc.riskcontrol.business.integration.UserApiClient;
import com.cmc.riskcontrol.business.message.AbnormalIpMailSendService;
import com.cmc.riskcontrol.business.utils.DateTimeUtils;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2022/11/14
 * @description
 **/
@Service
public class AbnormalIpMailSendServiceImpl implements AbnormalIpMailSendService {

    private final UserApiClient userApiClient;

    private final EmailSender emailSender;
    @Value("${com.cmc.risk.template-id:d-58c4028b0c1a4611b0abfa3f8601468b}")
    private String templateId;
    @Value("${com.cmc.risk.from-email:<EMAIL>}")
    private String fromEmail;

    private final static Set<String> LOGIN = Set.of("userLogin", "walletLogin", "binanceLogin");


    public AbnormalIpMailSendServiceImpl(UserApiClient userApiClient, EmailSender emailSender) {
        this.userApiClient = userApiClient;
        this.emailSender = emailSender;
    }

    /**
     *  send email if ip is abnormal
     * @param ip client ip
     * @param userId user id
     * @return
     */
    @Override
    public Mono<Boolean> sendMail(String ip, String userId) {
        return  userApiClient.getDeviceInfo(userId, ip)
            .map(item -> EmailContentVO.builder()
                    .tos(List.of(item.getEmail()))
                    .replyToEmail(fromEmail)
                    .fromEmail(fromEmail)
                    .templateId(templateId)
                    .dynamicTemplateData(Map.of(
                        "ip", ip,
                        "time", DateTimeUtils.transferUTCTimeAndFormat(item.getEventTime(),
                            DatetimeUtils.LocalDateFormatEnum.YYYY_MM_DD_HH_MM_SS),
                        "device", item.getDeviceName(),
                        "email", item.getEmail()))
                    .build())
            .flatMap(emailSender::sendMail);
    }
}
