package com.cmc.riskcontrol.business.message.impl;

import com.cmc.framework.kafka.client.KafkaProducer;
import com.cmc.framework.kafka.client.ProducerMessage;
import com.cmc.riskcontrol.business.message.EventMessageSendService;
import com.cmc.riskcontrol.model.kafka.PushMessageEvent;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2022/2/9 10:39
 */
@Service
@Slf4j
public class EventMessageSendServiceImpl implements EventMessageSendService {

    private final KafkaProducer kafkaProducer;

    public EventMessageSendServiceImpl(@Autowired(required = false) @Qualifier("event") KafkaProducer kafkaProducer) {
        this.kafkaProducer = kafkaProducer;
    }

    @Override
    public Mono<Boolean> send(PushMessageEvent messageEvent) {
        if (messageEvent == null || StringUtils.isAnyBlank(messageEvent.getKafkaTopicName(), messageEvent.getMessage())) {
            return Mono.empty();
        }

        UUID uuid = UUID.randomUUID();
        return kafkaProducer.send(messageEvent.getKafkaTopicName(),
            Mono.just(new ProducerMessage<>(uuid.toString(), messageEvent.getMessage(), messageEvent.getMessage())))
                            .doOnError(throwable -> log.error("push message to kafka failed.", throwable))
                            .thenReturn(true);
    }

}
