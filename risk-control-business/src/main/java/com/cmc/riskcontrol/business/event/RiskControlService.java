package com.cmc.riskcontrol.business.event;

import com.cmc.riskcontrol.model.dto.EventJudgeRequestDto;
import com.cmc.riskcontrol.model.dto.EventJudgeResponseDto;
import com.cmc.riskcontrol.model.dto.EventRecordRequestDto;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2022/2/8 10:57
 */
public interface RiskControlService {

    /**
     * record event
     * @param eventRecordRequestDto
     * @return
     */
    Mono<Boolean> record(EventRecordRequestDto eventRecordRequestDto);

    /**
     * judge event by risk control service
     * @param eventJudgeRequestDto
     * @return
     */
    Mono<EventJudgeResponseDto> judge(EventJudgeRequestDto eventJudgeRequestDto);

}
