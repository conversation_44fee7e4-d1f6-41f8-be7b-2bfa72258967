package com.cmc.riskcontrol.business.url;

import com.cmc.riskcontrol.model.dto.UrlValidationRequestDto;
import com.cmc.riskcontrol.model.dto.UrlValidationResponseDto;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/7/28 10:24
 */
public interface UrlService {

    /**
     * Validates whether the given URL is safe and does not match any predefined SSRF attack patterns.
     *
     * @param request request containing the URL to be validated
     * @return a {@link Mono} emitting a {@link UrlValidationResponseDto} indicating the validation result,
     *         including whether the URL is valid and any relevant messages
     */
    Mono<UrlValidationResponseDto> validateUrl(UrlValidationRequestDto request);
}
