package com.cmc.riskcontrol.business.event.impl;

import static com.cmc.riskcontrol.model.constant.Constants.CLIENT_IP;
import static com.cmc.riskcontrol.model.constant.Constants.LOGIN_TIME_STAMP;
import static com.cmc.riskcontrol.model.constant.Constants.USER_ID;
import static com.cmc.riskcontrol.model.enums.EventEnum.ABNORMAL_IP;

import com.cmc.riskcontrol.business.event.AbnormalIpService;
import com.cmc.riskcontrol.business.integration.BinanceCommonServiceClient;
import com.cmc.riskcontrol.business.message.AbnormalIpMailSendService;
import com.cmc.riskcontrol.model.dto.EventJudgeResponseDto;
import com.cmc.riskcontrol.model.enums.EventEnum;
import com.cmc.riskcontrol.model.rmi.CommonRuleRequestDto;
import com.cmc.riskcontrol.model.rmi.CommonRuleResponseDto;
import com.google.common.collect.Maps;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * <AUTHOR>
 * @date 2022/11/28
 * @description
 **/
@Service
@Slf4j
public class AbnormalIpServiceImpl implements AbnormalIpService {

    private final BinanceCommonServiceClient binanceCommonServiceClient;

    public AbnormalIpServiceImpl(BinanceCommonServiceClient binanceCommonServiceClient) {
        this.binanceCommonServiceClient = binanceCommonServiceClient;
    }

    /**
     *  determine whether the ip is abnormal
     * @param userId
     * @param ip
     * @param isNeedSendEmail
     * @param loginTime
     * @return
     */
    @Override
    public Mono<EventJudgeResponseDto> determineIp(Long userId, String ip, Boolean isNeedSendEmail,String loginTime) {
        CommonRuleRequestDto dto = new CommonRuleRequestDto();
        CommonRuleRequestDto.CommonRuleBody body = new CommonRuleRequestDto.CommonRuleBody();
        body.setEventCode(ABNORMAL_IP.getValue());
        Map<String,Object> objectMap = Maps.newHashMap();
        objectMap.put(USER_ID,userId);
        objectMap.put(CLIENT_IP, ip);
        objectMap.put(LOGIN_TIME_STAMP,loginTime);
        body.setContext(objectMap);
        dto.setBody(body);

        Predicate<EventJudgeResponseDto> predicate =
            eventJudgeResponseDto -> BooleanUtils.isTrue(eventJudgeResponseDto.getIsHit())
                && BooleanUtils.isTrue(isNeedSendEmail);
        return binanceCommonServiceClient.commonRule(dto)
            .map(this::getResponseDto);
    }

    @NotNull
    private  EventJudgeResponseDto getResponseDto(CommonRuleResponseDto res) {
        EventJudgeResponseDto eventJudgeResponseDto = new EventJudgeResponseDto();
        eventJudgeResponseDto.setIsHit(Optional.ofNullable(res.getIsHit()).orElse(false));

        if (res.getIsHit() != null) {
            log.info("event {} hit info {}", ABNORMAL_IP.getValue(), res.getHitRules());
        }
        return eventJudgeResponseDto;
    }
}
