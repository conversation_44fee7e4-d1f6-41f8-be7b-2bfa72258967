package com.cmc.riskcontrol.business.converter;

import com.cmc.riskcontrol.business.ip.IPService;
import com.cmc.riskcontrol.model.dto.EventRecordRequestDto;
import com.cmc.riskcontrol.model.dto.IPAddressDto;
import com.cmc.riskcontrol.model.kafka.EventMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/2/9 11:16
 */
@Component
public class EventRecordDtoConverter implements DataConverter<EventRecordRequestDto, EventMessage> {

    @Autowired
    private IPService ipService;

    @Override
    public EventMessage convert(EventRecordRequestDto input) {
        EventMessage eventMessage = new EventMessage();
        eventMessage.setUserId(input.getUserId());
        eventMessage.setTime(input.getTime());
        eventMessage.setEmail(input.getEmail());
        eventMessage.setFvideoId(input.getFvideoId());
        eventMessage.setClientType(input.getClientType());
        eventMessage.setAppVersion(input.getAppVersion());
        eventMessage.setUa(input.getUa());
        eventMessage.setBehavior(input.getBehavior());
        eventMessage.setExtendedInfo(input.getExtendedInfo());

        if (StringUtils.isNotBlank(input.getIp())) {
            IPAddressDto addressDto = ipService.parse(input.getIp());

            eventMessage.setIp(addressDto.getIp());
            eventMessage.setIpCountry(addressDto.getIpCountry());
            eventMessage.setIpRegion(addressDto.getIpRegion());
            eventMessage.setIpCity(addressDto.getIpCity());
        }
        return eventMessage;
    }
}
