package com.cmc.riskcontrol.business.integration;

import com.cmc.framework.common.model.ApiResponse;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.riskcontrol.model.dto.OldUserDTO;
import com.cmc.riskcontrol.model.dto.UserDeviceTimeResponseDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import java.time.Duration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2022/11/15
 * @description
 **/
@Component
@Slf4j
public class UserApiClient extends BaseServiceClient{
    private static final String GET_DEVICE_INFO = "/system/security-center/device/getDeviceByUserIdAndIp";

    @Value("${com.cmc.risk.user.url:https://api.beta.coinmarketcap.supply/user-info}")
    private String userApiBaseUrl;

    /**
     * send email
     * @param userId 用户id
     * @return
     */
    public Mono<UserDeviceTimeResponseDTO> getDeviceInfo(String userId, String ip) {
        String url = String.format(userApiBaseUrl + GET_DEVICE_INFO+"?uid="+ userId + "&ip=" + ip);
        return client.get(url)
            .map(response -> JacksonUtils.deserialize(response.getBody(),
                new TypeReference<ApiResponse<UserDeviceTimeResponseDTO>>() {
                }))
            .map(ApiResponse::getData)
            .defaultIfEmpty(UserDeviceTimeResponseDTO.builder().build())
            .doOnError(throwable -> log.error("get device info failed, userId：{}, ip:{}",userId, ip, throwable));
    }
}
