package com.cmc.riskcontrol.business.url;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/7/28 11:01
 */
@Slf4j
@Component
public class CustomSsrfPatternValidator {

    private static final String PATTERN_KEY = "com.cmc.riskcontrol.url.custom-patterns";

    @ApolloConfig
    private Config config;

    private final AtomicReference<List<Pattern>> compiledPatterns = new AtomicReference<>(Collections.emptyList());

    @PostConstruct
    public void init() {
        if (config == null) {
            try {
                config = ConfigService.getAppConfig();
            } catch (Throwable t) {
                log.warn("Apollo ConfigService not available during init. Skipping pattern load.");
            }
        }
        if (config == null) {
            log.warn("Apollo config is null. Patterns not initialized. Will wait for Apollo to be available.");
            return;
        }
        updatePatterns(config.getProperty(PATTERN_KEY, ""));
    }

    @ApolloConfigChangeListener(interestedKeys = {PATTERN_KEY})
    public void onConfigChange(ConfigChangeEvent changeEvent) {
        if (config == null) {
            // Try lazy init if Apollo becomes available later
            try {
                config = ConfigService.getAppConfig();
            } catch (Throwable ignored) { }
            if (config == null) {
                log.warn("Apollo config still null on change event. Skipping update.");
                return;
            }
        }
        if (!changeEvent.isChanged(PATTERN_KEY)) {
            return;
        }

        if (Thread.currentThread().isInterrupted()) {
            log.warn("SSRF pattern listener interrupted. Skipping update.");
            return;
        }

        log.info("Detected update to SSRF patterns via Apollo.");
        String updatedRawValue = config.getProperty(PATTERN_KEY, "");
        updatePatterns(updatedRawValue);
    }

    public List<Pattern> getCompiledPatterns() {
        return compiledPatterns.get();
    }

    private void updatePatterns(String rawValue) {
        List<String> rawPatterns = parseMultilineProperty(rawValue);
        List<Pattern> compiled = rawPatterns.stream()
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(pattern -> Pattern.compile(pattern, Pattern.CASE_INSENSITIVE))
                .collect(Collectors.toList());

        compiledPatterns.set(compiled);
        log.info("SSRF patterns updated. {} patterns loaded.", compiled.size());
    }

    private List<String> parseMultilineProperty(String value) {
        // 支持多行字符串或逗号分隔
        return List.of(value.split("\\r?\\n|,"));
    }
}
