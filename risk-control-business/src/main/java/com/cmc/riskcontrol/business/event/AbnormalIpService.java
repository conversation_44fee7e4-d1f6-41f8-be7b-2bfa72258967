package com.cmc.riskcontrol.business.event;

import com.cmc.riskcontrol.model.dto.EventJudgeResponseDto;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2022/11/28
 * @description
 **/
public interface AbnormalIpService {
    /**
     *  determine whether the ip is abnormal
     * @param userId
     * @param ip
     * @param isNeedSendEmail
     * @param loginTime
     * @return
     */
    Mono<EventJudgeResponseDto> determineIp(Long userId, String ip, Boolean isNeedSendEmail, String loginTime);
}
