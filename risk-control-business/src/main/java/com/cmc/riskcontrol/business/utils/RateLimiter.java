package com.cmc.riskcontrol.business.utils;

import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @since 2022/02/20
 */
public class RateLimiter {

    private int requestInterval;

    private long maxTokens;

    private final AtomicLong tokens;

    private final AtomicLong lastRefillTime;

    public RateLimiter(int maxRequests, int requestInterval) {
        this.requestInterval = requestInterval;
        this.maxTokens = maxRequests;
        tokens = new AtomicLong(maxRequests);
        lastRefillTime = new AtomicLong(System.currentTimeMillis());
    }

    public synchronized void refresh(int maxRequests, int requestInterval) {
        this.requestInterval = requestInterval;
        this.maxTokens = maxRequests;
        tokens.set(maxRequests);
        lastRefillTime.set(System.currentTimeMillis());
    }

    public boolean allowRequest() {
        long currentTokens = tokens.get();
        long newTokens = Math.min(currentTokens + refill(), maxTokens);

        if (newTokens >= 1) {
            return tokens.compareAndSet(currentTokens, newTokens - 1);
        }

        return false;
    }

    private long refill() {
        long now = System.currentTimeMillis();
        long elapsed = now - lastRefillTime.get();
        long tokensToAdd = elapsed * maxTokens / requestInterval;
        lastRefillTime.set(now);
        return tokensToAdd;
    }
}
