package com.cmc.riskcontrol.business.integration;

import com.cmc.framework.utils.JacksonUtils;
import com.cmc.riskcontrol.model.rmi.CommonRuleRequestDto;
import com.cmc.riskcontrol.model.rmi.CommonRuleResponseDto;
import java.util.function.Consumer;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2022/2/8 11:04
 */
@Component
public class BinanceCommonServiceClient extends BaseServiceClient {

    @Value("${com.cmc.riskcontrol.client.binance-api:}")
    private String url;
    @Value("${com.cmc.riskcontrol.client.binance-api.clientId:}")
    private String clientId;
    @Value("${com.cmc.riskcontrol.client.binance-api.accessToken:}")
    private String accessToken;

    public Mono<CommonRuleResponseDto> commonRule(CommonRuleRequestDto ruleRequestDto) {
        return client.post(String.format("%s/rule/saas/common/commonRule", url), ruleRequestDto, builderHeader())
                     .map(response -> JacksonUtils.deserialize(response.getBody(), CommonRuleResponseDto.class));
    }

    @NotNull
    private Consumer<HttpHeaders> builderHeader() {
        return header -> {
            header.set("Content-Type", "application/json");
            header.set("merchant", "cmc");
            header.set("X-Tesla-ClientId", clientId);
            header.set("X-Tesla-SignAccessToken", accessToken);
        };
    }

}
