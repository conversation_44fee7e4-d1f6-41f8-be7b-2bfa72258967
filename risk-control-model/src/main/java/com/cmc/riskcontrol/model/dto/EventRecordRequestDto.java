package com.cmc.riskcontrol.model.dto;

import com.cmc.framework.common.model.BaseRequest;
import java.util.Date;
import java.util.Map;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/2/8 10:44
 */
@Schema(description = "the request body of record event")
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventRecordRequestDto extends BaseRequest {

    @Schema(description = "userId", example = "5300154")
    private String userId;

    @Schema(description = "record timestamp")
    private Date time;

    @Schema(description = "user email")
    private String email;

    @Schema(description = "device id")
    private String fvideoId;

    @Schema(description = "client type: web/mobile")
    private String clientType;

    @Schema(description = "mobile app version")
    private String appVersion;

    @Schema(description = "web browser user agent")
    private String ua;

    @Schema(description = "client ip")
    private String ip;

    @Schema(description = "record behavior")
    private String behavior;

    @Schema(description = "extended info")
    private Map<String, Object> extendedInfo;

}
