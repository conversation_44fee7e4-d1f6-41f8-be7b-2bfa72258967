package com.cmc.riskcontrol.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/2/8 10:44
 */
@Schema(description = "the request body of judge event result")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventJudgeResponseDto {

    @Schema(description = "judge event is hit risk rule")
    private Boolean isHit;

    @Schema(description = "error message if hit risk rule")
    private String riskContent;
}
