package com.cmc.riskcontrol.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/28 10:26
 */
@Data
@Schema(description = "Url validation response DTO")
public class UrlValidationResponseDto {

    @Schema(description = "Whether the URL is valid")
    @JsonProperty("isValid")
    private Boolean isValid;

    @Schema(description = "Validation message")
    private String message;

    public UrlValidationResponseDto(boolean isValid, String message) {
        this.isValid = isValid;
        this.message = message;
    }

    @Override
    public String toString() {
        return "ValidationResult{" +
                "isValid=" + isValid +
                ", message='" + message + '\'' +
                '}';
    }
}
