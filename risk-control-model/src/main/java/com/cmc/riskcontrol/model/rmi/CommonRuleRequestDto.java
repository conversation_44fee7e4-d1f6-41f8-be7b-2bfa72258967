package com.cmc.riskcontrol.model.rmi;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/2/8 10:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonRuleRequestDto {

    private CommonRuleBody body;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CommonRuleBody {

        /**
         * 不同业务场景eventCode不同
         */
        private String eventCode;
        /**
         * 不同业务场景的额外信息
         * e.g. （CMC注册）market_risk_cousult：userId(string）,campName（string）
         */
        private Map<String, Object> context;
    }
}
