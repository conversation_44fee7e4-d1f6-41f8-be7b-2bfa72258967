package com.cmc.riskcontrol.model.kafka;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/2/9 10:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventMessage {

    @JsonProperty("user_id")
    private String userId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;
    private String email;
    @JsonProperty("fvideo_id")
    private String fvideoId;
    @JsonProperty("client_type")
    private String clientType;
    @JsonProperty("app_version")
    private String appVersion;
    private String ua;
    private String ip;
    @JsonProperty("ip_country")
    private String ipCountry;
    @JsonProperty("ip_region")
    private String ipRegion;
    @JsonProperty("ip_city")
    private String ipCity;
    private String behavior;
    @JsonProperty("extended_info")
    private Map<String, Object> extendedInfo;

}
