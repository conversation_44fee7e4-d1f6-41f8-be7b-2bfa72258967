package com.cmc.riskcontrol.model.dto;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/02/13
 * @description
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDeviceTimeResponseDTO {
    private String id;
    private String deviceId;
    private String deviceName;
    private String businessType;
    private Date eventTime;
    private String email;
    private String userName;
}
