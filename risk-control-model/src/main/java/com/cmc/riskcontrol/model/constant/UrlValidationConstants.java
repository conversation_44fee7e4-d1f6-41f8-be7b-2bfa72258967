package com.cmc.riskcontrol.model.constant;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/7/28 10:24
 */
public interface UrlValidationConstants {

    Pattern[] BLACKLIST_PATTERNS = {
        // 私有IP地址段
        Pattern.compile("^10\\.", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^172\\.(1[6-9]|2[0-9]|3[0-1])\\.", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^192\\.168\\.", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^127\\.", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^169\\.254\\.", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^0\\.0\\.0\\.0$", Pattern.CASE_INSENSITIVE),

        // 特殊IP地址
        Pattern.compile("^localhost$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^::1$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^127\\.0\\.0\\.1$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^255\\.255\\.255\\.255$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^22[4-9]\\.", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^23[0-9]\\.", Pattern.CASE_INSENSITIVE),

        // 云服务元数据端点
        Pattern.compile("169\\.254\\.169\\.254", Pattern.CASE_INSENSITIVE),
        Pattern.compile("http://169\\.254\\.169\\.254", Pattern.CASE_INSENSITIVE),
        Pattern.compile("http://metadata\\.amazonaws\\.com", Pattern.CASE_INSENSITIVE),
        Pattern.compile("http://metadata\\.aws\\.amazon\\.com", Pattern.CASE_INSENSITIVE),
        Pattern.compile("http://metadata\\.azure\\.com", Pattern.CASE_INSENSITIVE),
        Pattern.compile("http://metadata\\.google\\.internal", Pattern.CASE_INSENSITIVE),
        Pattern.compile("http://metadata\\.compute\\.internal", Pattern.CASE_INSENSITIVE),
        Pattern.compile("100\\.100\\.100\\.200", Pattern.CASE_INSENSITIVE),
        Pattern.compile("http://100\\.100\\.100\\.200", Pattern.CASE_INSENSITIVE),
        Pattern.compile("169\\.254\\.0\\.2", Pattern.CASE_INSENSITIVE),
        Pattern.compile("http://169\\.254\\.0\\.2", Pattern.CASE_INSENSITIVE),

        // Collaborator 服务 (Burp Suite, OAST 工具等)
        Pattern.compile("burpcollaborator\\.net", Pattern.CASE_INSENSITIVE),
        Pattern.compile("canarytokens\\.com", Pattern.CASE_INSENSITIVE),
        Pattern.compile("canarytokens\\.org", Pattern.CASE_INSENSITIVE),
        Pattern.compile("webhook\\.site", Pattern.CASE_INSENSITIVE),
        Pattern.compile("requestbin\\.net", Pattern.CASE_INSENSITIVE),
        Pattern.compile("requestbin\\.com", Pattern.CASE_INSENSITIVE),
        Pattern.compile("httpbin\\.org", Pattern.CASE_INSENSITIVE),
        Pattern.compile("httpbin\\.com", Pattern.CASE_INSENSITIVE),
        Pattern.compile("postb\\.in", Pattern.CASE_INSENSITIVE),
        Pattern.compile("hook\\.in", Pattern.CASE_INSENSITIVE),
        Pattern.compile("pipedream\\.net", Pattern.CASE_INSENSITIVE),
        Pattern.compile("ngrok\\.io", Pattern.CASE_INSENSITIVE),
        Pattern.compile("ngrok\\.com", Pattern.CASE_INSENSITIVE),
        Pattern.compile("localtunnel\\.me", Pattern.CASE_INSENSITIVE),
        Pattern.compile("serveo\\.net", Pattern.CASE_INSENSITIVE),
        Pattern.compile("xip\\.io", Pattern.CASE_INSENSITIVE),
        Pattern.compile("nip\\.io", Pattern.CASE_INSENSITIVE),
        Pattern.compile("sslip\\.io", Pattern.CASE_INSENSITIVE),
        Pattern.compile("vcap\\.me", Pattern.CASE_INSENSITIVE),
        Pattern.compile("vcap\\.io", Pattern.CASE_INSENSITIVE),
        Pattern.compile("loca\\.lt", Pattern.CASE_INSENSITIVE),
        Pattern.compile("loca\\.l", Pattern.CASE_INSENSITIVE),
        Pattern.compile("localhost\\.run", Pattern.CASE_INSENSITIVE),
        Pattern.compile("localhost\\.run", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.to", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.com", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.org", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.net", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.io", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.me", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.in", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.ru", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.cn", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.jp", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.kr", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.de", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.fr", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.uk", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.ca", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.au", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.br", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.mx", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.ar", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.cl", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.pe", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.uk", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.za", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.in", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.jp", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.kr", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.de", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.fr", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.ca", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.au", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.br", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.mx", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.ar", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.cl", Pattern.CASE_INSENSITIVE),
        Pattern.compile("tunnel\\.co\\.pe", Pattern.CASE_INSENSITIVE),

        // 内部服务端口
        Pattern.compile(":22$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":21$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":23$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":25$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":53$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":80$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":110$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":143$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":443$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":993$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":995$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":1433$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":1521$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":3306$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":3389$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":5432$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":6379$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":8080$", Pattern.CASE_INSENSITIVE),
        Pattern.compile(":27017$", Pattern.CASE_INSENSITIVE),

        // 危险协议
        Pattern.compile("^file://", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^ftp://", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^gopher://", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^dict://", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^ldap://", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^ldaps://", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^tftp://", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^telnet://", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^ssh://", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^sftp://", Pattern.CASE_INSENSITIVE),

        // 特殊域名和主机名
        Pattern.compile("\\.local$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.internal$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.intranet$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.corp$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.home$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.lan$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^host\\.docker\\.internal$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^gateway\\.docker\\.internal$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^docker\\.internal$", Pattern.CASE_INSENSITIVE),

        // 特殊路径和查询参数
        Pattern.compile("/latest/meta-data", Pattern.CASE_INSENSITIVE),
        Pattern.compile("/metadata", Pattern.CASE_INSENSITIVE),
        Pattern.compile("/instance", Pattern.CASE_INSENSITIVE),
        Pattern.compile("/security-credentials", Pattern.CASE_INSENSITIVE),
        Pattern.compile("/iam/security-credentials", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.env", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.ini$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.conf$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.config$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.xml$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.json$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.yaml$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\.yml$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("/etc/", Pattern.CASE_INSENSITIVE),
        Pattern.compile("/proc/", Pattern.CASE_INSENSITIVE),
        Pattern.compile("/sys/", Pattern.CASE_INSENSITIVE),
        Pattern.compile("/var/log/", Pattern.CASE_INSENSITIVE),
        Pattern.compile("/tmp/", Pattern.CASE_INSENSITIVE),
        Pattern.compile("/dev/", Pattern.CASE_INSENSITIVE),

        // 特殊字符和编码绕过
        Pattern.compile("0x[a-f0-9]{8}", Pattern.CASE_INSENSITIVE),
        Pattern.compile("0[0-7]{10}", Pattern.CASE_INSENSITIVE),
        Pattern.compile("[0-9]{10}", Pattern.CASE_INSENSITIVE),
        Pattern.compile("%2e%2e", Pattern.CASE_INSENSITIVE),
        Pattern.compile("%2f", Pattern.CASE_INSENSITIVE),
        Pattern.compile("%5c", Pattern.CASE_INSENSITIVE),
        Pattern.compile("%00", Pattern.CASE_INSENSITIVE),
        Pattern.compile("%0a", Pattern.CASE_INSENSITIVE),
        Pattern.compile("%0d", Pattern.CASE_INSENSITIVE),

        // 常见绕过技术
        Pattern.compile("redirect=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("url=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("next=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("target=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("goto=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("link=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("continue=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("%[0-9a-fA-F]{2}", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\\\x[0-9a-fA-F]{2}", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\\\u[0-9a-fA-F]{4}", Pattern.CASE_INSENSITIVE),
        Pattern.compile("\\\\U[0-9a-fA-F]{8}", Pattern.CASE_INSENSITIVE),

        // 组合模式 (更严格的检测)
        Pattern.compile("^127\\.0\\.0\\.1:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^localhost:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^10\\..*:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^192\\.168\\..*:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^169\\.254\\..*:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("file://.*\\.(env|ini|conf|xml|json|yaml|yml)$", Pattern.CASE_INSENSITIVE),
        Pattern.compile("http://.*\\.(local|internal|intranet|corp|home|lan)$", Pattern.CASE_INSENSITIVE)
    };

}
