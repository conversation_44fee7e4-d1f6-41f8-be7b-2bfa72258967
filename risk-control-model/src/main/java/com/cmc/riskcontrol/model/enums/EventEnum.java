package com.cmc.riskcontrol.model.enums;

import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/2/8 10:53
 */
@AllArgsConstructor
@Getter
@Slf4j
public enum EventEnum {

    REGISTER(0, "market_risk_cousult"),
    ABNORMAL_IP(1, "cmc_user_login_event"),
    ;

    private final Integer code;

    private final String value;

    public static EventEnum findByCode(Integer eventCode) {
        if (eventCode == null) {
            return null;
        }
        return Arrays.stream(EventEnum.values())
                     .filter(e -> e.code.equals(eventCode))
                     .findAny()
                     .orElseGet(() -> {
                         log.error("EventEnums,The value mismatch:", eventCode);
                         return null;
                     });
    }
}
