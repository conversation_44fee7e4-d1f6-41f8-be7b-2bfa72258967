package com.cmc.riskcontrol.model.dto;

import com.cmc.framework.common.model.BaseRequest;
import java.util.Map;
import jakarta.validation.constraints.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/2/8 10:44
 */
@Schema(description = "the request body of judge event risk")
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventJudgeRequestDto extends BaseRequest {

    @Schema(description = "event code")
    @NotNull
    private Integer eventCode;

    @Schema(description = "event context")
    private Map<String, Object> context;
}
