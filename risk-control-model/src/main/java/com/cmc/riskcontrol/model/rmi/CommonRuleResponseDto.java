package com.cmc.riskcontrol.model.rmi;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/2/8 10:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonRuleResponseDto {

    /**
     * 是否命中风控规则
     */
    private Boolean isHit;
    /**
     * 从规则返回的额外信息
     * e.g. riskContent	//返回命中风控后的文案
     */
    private Map<String, Object> extend;
    /**
     * 返回规则命中结果，key 为规则名称，value 为规则命中结果，true代表命中，null或者false 代表非命中
     */
    private Map<String, Boolean> hitRules;

}
