package com.cmc.riskcontrol.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/2/15 16:21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "the request body of parse ip result")
public class IPAddressDto {
    @Schema(description = "the ip to parse")
    private String ip;
    @Schema(description = "the country of parsing ip")
    private String ipCountry;
    @Schema(description = "the region of parsing ip")
    private String ipRegion;
    @Schema(description = "the city of parsing ip")
    private String ipCity;

}
