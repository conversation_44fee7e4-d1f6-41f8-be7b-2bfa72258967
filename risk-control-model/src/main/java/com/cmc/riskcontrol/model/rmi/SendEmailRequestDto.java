package com.cmc.riskcontrol.model.rmi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/11/11
 * @description
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendEmailRequestDto {
    private String ip;
    private String ipCountry;
    private String ipRegion;
    private String ipCity;
    private String userId;
    private String email;
}
