package com.cmc.riskcontrol.model.dto;

import com.cmc.framework.common.model.BaseResponse;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

 /**
  * <AUTHOR>
  * @description
  * @date  2022/11/15
  */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OldUserDTO extends BaseResponse {
    private String id;

    private String email;

    private String username;
    
    private String nickname;

    private String password;

    private String keyPlan;

    private String signUpPlanId;

    private Boolean isActive;

    private Boolean emailVerified;

    private Boolean verifiedByCMC;

    private Date timeCreated;

    private Date lastLogin;

    private String avatarId;

    private List<String> roles;

    private Integer type;

    private Long uid;

    private Date lastUpdateName;

    private Date lastUpdateNickname;

    private Date lastUpdateAvatarId;

    private Boolean usernameChanged;

    private Boolean nicknameChanged;

    private Boolean avatarChanged;

    private String origin;

}
