spring.profiles.active=beta
spring.resources.chain.html-application-cache=true
spring.resources.chain.enabled=true
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024
server.http2.enabled=true
server.port=8801
server.env=beta
server.servlet.context-path=/
spring.application.name=cmc-risk-control-job

spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration

# Amazon s3
aws.s3.aws.region=us-east-1
aws.s3.aws.access.key.id=
aws.s3.aws.secret.access.key=

# file
com.cmc.riskcontrol.file.localPath=/tmp
com.cmc.riskcontrol.file.mmdb.url=https://download.maxmind.com/app/geoip_download?edition_id=GeoIP2-City&license_key=LB4rYY8L5qYSCfaB&suffix=tar.gz
com.cmc.riskcontrol.file.mmdb.s3-path=ip/GeoIP2-City.mmdb
