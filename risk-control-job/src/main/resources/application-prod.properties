server.env=prod
# mongo db connection string
service.env=prod

#auth
auth.api.base.url=https://api.prod.coinmarketcap.supply/auth

# Xxl-job config
xxl.job.admin.addresses=http://xxl-job-admin:8080/xxl-job-admin
xxl.job.executor.appname=risk-control-job

# Amazon s3
aws.s3.bucket = s2.coinmarketcap.com

com.cmc.riskcontrol.aws.region = us-east-1
com.cmc.riskcontrol.aws.access-key-id =
com.cmc.riskcontrol.aws.secret-access-key =
com.cmc.riskcontrol.aws.s3.bucket = s3.coinmarketcap.com
cmc.boot.kafka.settings.binance.bootstrapServers = ************:9092,*************:9092,************:9092
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.keyDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.valueDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.autoOffsetReset = latest
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.sessionTimeout = 300000
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.heartbeatInterval = 3000
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.maxPollRecords = 100
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.requestTimeout = 60000
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.enableAutoCommit = true
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.maxPollInterval = 120000
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.clientId = riskJob
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.groupId = riskJobGroup
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.subscribe = com.cmc.riskcontrol.job.consumer.BinanceRiskIpConsumer