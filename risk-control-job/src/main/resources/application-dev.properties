server.env=beta
# mongo db connection string
service.env=beta

#auth
auth.api.base.url= https://api.beta.coinmarketcap.supply/auth

# Xxl-job config
xxl.job.admin.addresses=http://xxl.beta.coinmarketcap.supply/xxl-job-admin
xxl.job.executor.appname=risk-control-job

# Amazon s3
aws.s3.bucket = testingstatic.coinmarketcap.com

com.cmc.riskcontrol.aws.region = us-east-1
com.cmc.riskcontrol.aws.access-key-id =
com.cmc.riskcontrol.aws.secret-access-key =
com.cmc.riskcontrol.aws.s3.bucket = s3.beta.coinmarketcap.com
