server.env=beta
# mongo db connection string
service.env=beta

# Apollo
app.id = cmc-risk-control-job
apollo.bootstrap.enabled = true
apollo.bootstrap.namespaces = application,backend.common,backend.influxdb

#auth
auth.api.base.url= https://api.beta.coinmarketcap.supply/auth

# Xxl-job config
xxl.job.admin.addresses=http://xxl.beta.coinmarketcap.supply/xxl-job-admin
xxl.job.executor.appname=risk-control-job

# Amazon s3
aws.s3.bucket = testingstatic.coinmarketcap.com

com.cmc.riskcontrol.aws.region = us-east-1
com.cmc.riskcontrol.aws.access-key-id =
com.cmc.riskcontrol.aws.secret-access-key =
com.cmc.riskcontrol.aws.s3.bucket = s3.beta.coinmarketcap.com
#cmc_exchange_created_events

cmc.boot.kafka.settings.risk.bootstrapServers=msk.beta.coinmarketcap.supply:9092
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.keyDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.valueDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.autoOffsetReset = latest
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.sessionTimeout = 300000
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.heartbeatInterval = 3000
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.maxPollRecords = 100
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.requestTimeout = 60000
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.enableAutoCommit = false
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.maxPollInterval = 120000
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.clientId = riskJob
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.groupId = riskJobGroup
cmc.boot.kafka.settings.risk.consumer.cmc-request-log.subscribe = com.cmc.riskcontrol.job.consumer.AbnormalIpConsumer
cmc.boot.kafka.settings.binance.bootstrapServers = *************:9092,*************:9092,************:2181:9092
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.keyDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.valueDeserializer = org.apache.kafka.common.serialization.StringDeserializer
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.autoOffsetReset = latest
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.sessionTimeout = 300000
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.heartbeatInterval = 3000
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.maxPollRecords = 100
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.requestTimeout = 60000
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.enableAutoCommit = true
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.maxPollInterval = 120000
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.clientId = riskJob
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.groupId = riskJobGroup
cmc.boot.kafka.settings.binance.consumer.cmc_user_login_ip_abnormal_detection.subscribe = com.cmc.riskcontrol.job.consumer.BinanceRiskIpConsumer
com.cmc.riskcontrol.client.binance-api = https://r.qa1fdg.net
com.cmc.riskcontrol.client.binance-api.clientId = cmc
com.cmc.riskcontrol.client.binance-api.accessToken = BuokhxYJrwSPYKMS