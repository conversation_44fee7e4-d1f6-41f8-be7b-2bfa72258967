<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <conversionRule conversionWord="msg" converterClass="com.cmc.framework.log.CmcMessageConverter"/>
    <property name="LOG_HOME" value="${LOG_HOME:-/data/logs/cmc-risk-control-job}"/>
    <property name="HOSTNAME" value="${HOSTNAME:-localhost}"/>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <target>System.out</target>
        <encoder class="com.cmc.framework.log.CmcPatternLayoutEncoder"/>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${HOSTNAME}-app.log</file>
        <append>true</append>
        <encoder class="com.cmc.framework.log.CmcPatternLayoutEncoder"/>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/${HOSTNAME}-app.%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
            <MaxHistory>60</MaxHistory>
            <totalSizeCap>4GB</totalSizeCap>
            <maxFileSize>10MB</maxFileSize>
        </rollingPolicy>

    </appender>

    <!-- Error logs appender -->
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${HOSTNAME}-error.log</file>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder class="com.cmc.framework.log.CmcPatternLayoutEncoder"/>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/${HOSTNAME}-error.%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
            <MaxHistory>60</MaxHistory>
            <totalSizeCap>4GB</totalSizeCap>
            <maxFileSize>10MB</maxFileSize>
        </rollingPolicy>
    </appender>

    <!-- This way <logger...> is used to output debug informs from package com.heywoof -->
    <logger name="com.cmc">
        <level value="info"/>
    </logger>

    <logger name="dev.morphia">
        <level value="warn"/>
    </logger>

    <logger name="org.mongodb.driver">
        <level value="warn"/>
    </logger>

    <logger name="org.apache.commons.httpclient">
        <level value="fatal"/>
    </logger>

    <logger name="httpclient.wire.header">
        <level value="fatal"/>
    </logger>

    <logger name="httpclient.wire.content">
        <level value="fatal"/>
    </logger>

    <logger name="org.springframework">
        <level value="info"/>
    </logger>

    <logger name="org.hibernate">
        <level value="error"/>
    </logger>

    <!-- disable http support log -->
    <logger name="org.apache.http">
        <level value="warn"/>
    </logger>

    <logger name="org.apache.kafka">
        <level value="info"/>
    </logger>
    <!-- Append-ref keyword makes the below appenders effect -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR"/>
    </root>
</configuration>
