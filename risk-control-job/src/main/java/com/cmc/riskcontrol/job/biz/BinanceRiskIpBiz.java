package com.cmc.riskcontrol.job.biz;

import com.cmc.framework.utils.JacksonUtils;
import com.cmc.riskcontrol.business.message.AbnormalIpMailSendService;
import com.cmc.riskcontrol.job.dto.BinanceRiskIpDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.Objects;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/02/09
 * @description
 **/
@Slf4j
@Service
public class BinanceRiskIpBiz {
    private final AbnormalIpMailSendService abnormalIpMailSendService;
    @Value("${com.cmc.riskcontrol.job.biz.send-email-flag:false}")
    private Boolean sendEmailFlag;
    public BinanceRiskIpBiz(AbnormalIpMailSendService abnormalIpMailSendService) {
        this.abnormalIpMailSendService = abnormalIpMailSendService;
    }

    /**
     * 消息模版
     * {
     *     "model_res_table_name": "cmc_login_event_w_res",
     *     "ip": "*************",
     *     "ip_in_blacklist": 0,
     *     "ip_country": "AU",
     *     "user_country_lst_add": null,
     *     "userId": "1437106",
     *     "procTime": "2023-02-16 07:21:47.863Z",
     *     "request_time": 1676532107,
     *     "user_country_lst": null,
     *     "is_deleted": null,
     *     "indexed_at": "2023-02-16T07:21:48.705296Z",
     *     "login_country_notIn_pre_lst": 0,
     *     "is_abnormal": 0,
     *     "feature_key": "7d1e6b40ddda491c0c20aee0dabcffbc",
     *     "feature_key_raw": "cmc_login_user_to_country.1437106",
     *     "feature_key_ip": "4f61e790696ada5683799ffaaebc129e"
     *   }
     * @param value
     */
    public void handelSingleMsg(String value) {
        BinanceRiskIpDTO binanceRiskIpDTO = JacksonUtils.deserialize(value, new TypeReference<BinanceRiskIpDTO>() {
        });
        if (Objects.nonNull(binanceRiskIpDTO) && BooleanUtils.isTrue(sendEmailFlag) && binanceRiskIpDTO.getIs_abnormal() == 1) {
            abnormalIpMailSendService.sendMail(binanceRiskIpDTO.getIp(), binanceRiskIpDTO.getUserId()).subscribe();
        }
    }
}
