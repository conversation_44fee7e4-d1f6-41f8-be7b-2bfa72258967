package com.cmc.riskcontrol.job.utils;


import static com.cmc.auth.common.utils.JwtTokenUtils.getTokenInfo;

import com.cmc.auth.common.utils.JwtTokenUtils;
import com.cmc.framework.utils.StringUtils;
import java.util.Map;
import org.springframework.http.HttpHeaders;

/**
 * <AUTHOR>
 * @date 2022/12/02
 * @description
 **/
public class ServerRequestUtil {
    public static final String PWD_ENCRYPTION_HEADER = "x-pwd-encryption";
    public static String getRealIp(Map<String,String> headers) {
        String ip = headers.get("x-forwarded-for");
        //Cloudflare reported client ip header
        String cf = headers.get("cf-connecting-ip");
        String xri = headers.get("x-real-ip");
        String xci = headers.get("x-cmc-ip");
        //Note we must use the last ip in the array to get the true IP.
        //otherwise it's a security issue as spoofing can happen.
        if (!StringUtils.isEmpty(ip)) {
            String[] ips = ip.split(",");
            ip = ips[0];
        } else if (!StringUtils.isEmpty(xri)) {
            String[] ips = xri.split(",");
            ip = ips[0];
        }

        if (!StringUtils.isEmpty(xci)) {
            ip = xci;
        } else if (!StringUtils.isEmpty(cf)) {
            ip = cf;
        }
        return ip;
    }

    public static Long getUid(Map<String,String> headers) {
        String authorization = headers.get("Authorization");
        if (StringUtils.isBlank(authorization)){
            return null;
        }
        JwtTokenUtils.TokenInfo tokenInfo = getTokenInfo(authorization);
        return tokenInfo.getUid();
    }
}

