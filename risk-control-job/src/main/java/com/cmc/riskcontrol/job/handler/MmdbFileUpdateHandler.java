package com.cmc.riskcontrol.job.handler;

import com.cmc.framework.common.http.HttpWebClient;
import com.cmc.framework.s3.CmcS3Client;
import com.cmc.riskcontrol.job.dto.JobHandlingResult;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.util.Optional;
import java.util.stream.Stream;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

/**
 * <AUTHOR>
 * @since 2022/2/14 17:18
 */
@Service
@Slf4j
public class MmdbFileUpdateHandler {

    @Autowired
    private HttpWebClient httpWebClient;
    @Autowired
    private CmcS3Client cmcS3Client;

    @Value("${com.cmc.riskcontrol.file.localPath:/tmp}")
    private String LOCAL_TEMP_PATH;

    @Value("${com.cmc.riskcontrol.file.mmdb.url}")
    private String mmdbFileURL;

    @Value("${com.cmc.riskcontrol.file.mmdb.s3-path}")
    private String mmdbS3Path;

    private static final String MMDB_FILE_EXTENSION = "mmdb";

    public JobHandlingResult<Boolean> handle() {
        try {
            Path archive = downloadFile();
            log.info("download success, path : {}", archive);
            decompress(archive).ifPresent(path -> {
                log.info("decompress && find mmdb file success, path : {}", path.toUri().getRawPath());
                uploaToS3(path);
            });
        } catch (IOException e) {
            log.error("occurs io execute error", e);
        }
        return new JobHandlingResult(true);
    }

    private Path downloadFile() throws IOException {
        Path archive = Paths.get(LOCAL_TEMP_PATH + "/GeoIP2-City.tar.gz");
        if (!Files.exists(archive)) {
            Files.createFile(archive);
        }

        Flux<DataBuffer> dataBuffer =
            httpWebClient.getWebClient().get().uri(mmdbFileURL).retrieve().bodyToFlux(DataBuffer.class);
        DataBufferUtils.write(dataBuffer, archive, StandardOpenOption.SYNC).block();
        return archive;
    }

    private Optional<Path> decompress(Path source) throws IOException {
        Path target = Paths.get(LOCAL_TEMP_PATH + "/GeoIP2-City/");
        if (Files.notExists(target)) {
            Files.createDirectories(target);
        }

        try (InputStream fi = Files.newInputStream(source);
            BufferedInputStream bi = new BufferedInputStream(fi);
            GzipCompressorInputStream gzi = new GzipCompressorInputStream(bi);
            TarArchiveInputStream ti = new TarArchiveInputStream(gzi)) {

            ArchiveEntry entry;
            while ((entry = ti.getNextEntry()) != null) {
                // create a new path, zip slip validate
                Path newPath = zipSlipProtect(entry, target);
                if (entry.isDirectory()) {
                    Files.createDirectories(newPath);
                } else {
                    // check parent folder again
                    Path parent = newPath.getParent();
                    if (parent != null) {
                        if (Files.notExists(parent)) {
                            Files.createDirectories(parent);
                        }
                    }
                    // copy TarArchiveInputStream to Path newPath
                    Files.copy(ti, newPath, StandardCopyOption.REPLACE_EXISTING);
                }
            }
        }
        //find mmdb file by extension
        Optional<Path> result;
        try (Stream<Path> walk = Files.walk(target, 2)) {
            result = walk.filter(p -> !Files.isDirectory(p))
                         .filter(p -> p.toString().toLowerCase().endsWith(MMDB_FILE_EXTENSION))
                         .findAny();
        }
        return result;
    }

    private static Path zipSlipProtect(ArchiveEntry entry, Path targetDir) throws IOException {
        Path targetDirResolved = targetDir.resolve(entry.getName());
        // make sure normalized file still has targetDir as its prefix, else throws exception
        Path normalizePath = targetDirResolved.normalize();
        if (!normalizePath.startsWith(targetDir)) {
            throw new IOException("Bad entry: " + entry.getName());
        }
        return normalizePath;
    }

    private void uploaToS3(Path target) {
        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
            .bucket("")
            .key(mmdbS3Path)
            .build();
        cmcS3Client.compareAndPutObject(putObjectRequest, target, "your business tag", null)

    }

}
