package com.cmc.riskcontrol.job.service.jobadmin.impl;

import com.cmc.riskcontrol.job.dto.JobAdminDTO;
import com.cmc.riskcontrol.job.service.jobadmin.JobAdminService;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.IJobHandler;
import java.util.concurrent.CompletableFuture;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/2/14 17:18
 */
@Service
@Slf4j
public class JobAdminServiceImpl implements JobAdminService {

    @Override
    public Boolean executeJobHandler(JobAdminDTO jobAdminDTO) {
        CompletableFuture.runAsync(() -> {
            IJobHandler jobHandler = XxlJobExecutor.loadJobHandler(jobAdminDTO.getJobName());
            try {
                XxlJobContext xxlJobContext = new XxlJobContext(-1, jobAdminDTO.getJobName(), null,  1, 1);
                XxlJobContext.setXxlJobContext(xxlJobContext);
                jobHandler.execute();
            } catch (Throwable e) {
                log.error("executeJobHandler error jobName: {}", jobAdminDTO.getJobName(), e);
            }
        });
        return true;
    }
}
