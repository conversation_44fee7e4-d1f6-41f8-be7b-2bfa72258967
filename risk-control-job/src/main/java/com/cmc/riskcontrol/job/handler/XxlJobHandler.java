package com.cmc.riskcontrol.job.handler;

import com.cmc.riskcontrol.job.dto.JobHandlingResult;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/2/14 17:18
 */
@Component
@Slf4j
public class XxlJobHandler {

    @Autowired
    private MmdbFileUpdateHandler mmdbFileUpdateHandler;

    @XxlJob("mmdbFileUpdateHandler")
    public ReturnT<String> mmdbFileUpdateHandler(String param) {
        JobHandlingResult<Boolean> result = mmdbFileUpdateHandler.handle();
        if (result.isSucceeded()) {
            return ReturnT.SUCCESS;
        } else {
            return new ReturnT<>(ReturnT.FAIL_CODE, result.getErrorMsg());
        }
    }

}
