package com.cmc.riskcontrol.job.dto;

import java.util.Date;
import lombok.Data;

/**
 * {
 *     "model_res_table_name": "cmc_login_event_w_res",
 *     "ip": "*************",
 *     "ip_in_blacklist": 0,
 *     "ip_country": "AU",
 *     "user_country_lst_add": null,
 *     "userId": "1437106",
 *     "procTime": "2023-02-16 07:21:47.863Z",
 *     "request_time": 1676532107,
 *     "user_country_lst": null,
 *     "is_deleted": null,
 *     "indexed_at": "2023-02-16T07:21:48.705296Z",
 *     "login_country_notIn_pre_lst": 0,
 *     "is_abnormal": 0,
 *     "feature_key": "7d1e6b40ddda491c0c20aee0dabcffbc",
 *     "feature_key_raw": "cmc_login_user_to_country.1437106",
 *     "feature_key_ip": "4f61e790696ada5683799ffaaebc129e"
 *   }
 * <AUTHOR>
 * @date 2023/02/09
 * @description
 **/
@Data
public class BinanceRiskIpDTO {
    private String model_res_table_name;
    private Long request_time;
    private String ip;
    private String ip_country;
    private String userId;
    private String user_country_lst;
    private Integer is_abnormal;
}
