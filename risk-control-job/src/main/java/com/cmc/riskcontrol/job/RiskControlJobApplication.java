package com.cmc.riskcontrol.job;

import com.cmc.riskcontrol.business.converter.EventRecordDtoConverter;
import com.cmc.riskcontrol.business.event.RiskControlService;
import com.cmc.riskcontrol.business.message.EventMessageSendService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;

/**
 * <AUTHOR>
 * @date 2022-01-13 15:06
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.cmc.riskcontrol.*", "com.cmc.framework.job", "com.cmc.email.sdk.*"}, excludeFilters = {
    @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {EventMessageSendService.class,
        RiskControlService.class, EventRecordDtoConverter.class})})
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class RiskControlJobApplication {
    public static void main(String[] args) {
        SpringApplication.run(RiskControlJobApplication.class, args);
    }
}
