package com.cmc.riskcontrol.job.consumer;

import com.cmc.framework.kafka.client.ConsumerAfterCommit;
import com.cmc.riskcontrol.job.biz.AbnormalIpBiz;
import jakarta.annotation.Resource;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

 /**
  * <AUTHOR>
  * @description
  * @date  2022/11/25
  */
@Slf4j
@Component
public class AbnormalIpConsumer implements ConsumerAfterCommit<String, String> {


    @Resource
    private AbnormalIpBiz abnormalIpBiz;

     @Override
     public void pull(ConsumerRecord<String, String> consumerRecord) {
         try {
             abnormalIpBiz.handelSingleMsg(consumerRecord.value());
         } catch (Throwable e) {
             log.error("There was an error in AbnormalIp before commit", e);
         }
     }
 }
