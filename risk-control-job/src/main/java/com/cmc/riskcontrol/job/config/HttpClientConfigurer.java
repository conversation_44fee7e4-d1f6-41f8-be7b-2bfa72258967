package com.cmc.riskcontrol.job.config;

import com.cmc.framework.common.http.HttpWebClient;
import com.cmc.framework.common.http.HttpWebClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 * @date 2021/8/2 14:44
 * @description
 */
@Configuration
public class HttpClientConfigurer {

    /**
     * get the reactive http client
     *
     * @return
     */
    @Bean
    @Order(0)
    public HttpWebClient createWebClient() {

        return HttpWebClientBuilder
                .builder()
                .isSecure(true)
                .acquireTimeout(50000)
                .maxConnections(30000)
                .readTimeoutSeconds(60)
                .writeTimeoutSeconds(60)
                .connectTimeoutMillis(60000)
                .build();
    }
}
