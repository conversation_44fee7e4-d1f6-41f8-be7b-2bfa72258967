package com.cmc.riskcontrol.job.biz;

import static com.cmc.riskcontrol.job.utils.ServerRequestUtil.getRealIp;
import static com.cmc.riskcontrol.job.utils.ServerRequestUtil.getUid;

import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import com.cmc.riskcontrol.business.event.AbnormalIpService;
import com.cmc.riskcontrol.job.dto.KafkaMessage;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AbnormalIpBiz {

    @Resource
    private AbnormalIpService abnormalIpService;


    /**
     * sample rate, total rate is 1000, sample rate needs 0-1000;
     */
    @Value("${com.cmc.riskjob.sample-rate:100}")
    private Integer sampleRate = 100;
    /**
     * total sample ,default is 1000
     */
    @Value("${com.cmc.riskjob.total-sample:1000}")
    private Integer totalSample = 1000;
    /**
     * if hit abnormal ip and hit need check url, will send user email.
     */
    @Value("#{'${com.cmc.riskjob.need-check-abnormal-urls:/user/login,/user/update/info,/old/info}'.split(',')}")
    private Set<String> needCheckAbnormalUrls = new HashSet<>();


    public void handelSingleMsg(String ss) {
        final KafkaMessage kafkaMessage = JacksonUtils.deserialize(ss, new TypeReference<>() {
        });
        final String requestTime = kafkaMessage.getT();
        final Map<String, Object> messageObj = kafkaMessage.getD();
        if (StringUtils.isBlank(requestTime) || CollectionUtils.isEmpty(messageObj)) {
            return;
        }
        Map<String,String> httpHeader = (Map<String, String>)messageObj.getOrDefault("headers", new Object());
        String url = (String)messageObj.getOrDefault("url", "");
        if (CollectionUtils.isEmpty(httpHeader) || StringUtils.isBlank(url)){
            return;
        }
        String realIp = getRealIp(httpHeader);
        Long userId = getUid(httpHeader);
        if (StringUtils.isAnyBlank(realIp)|| Objects.isNull(userId)){
            return;
        }
        // Convert milliseconds to seconds
        final String loginTime = requestTime.substring(0,requestTime.length()-3);
        boolean isNeedSendEmail = containsUrl(needCheckAbnormalUrls, url);
        if (!isNeedSendEmail && !hitSample(sampleRate, totalSample)) {
            return;
        }


        abnormalIpService.determineIp(userId, realIp, isNeedSendEmail,loginTime).subscribe();
    }
    /**
     *  Determine whether the current request hits the sample
     * @param sampleRate sample rate (0-totalSample)
     * @param totalSample total sample
     * @return hit is true and not hit is false
     */
    private boolean hitSample(Integer sampleRate, Integer totalSample) {
        return RandomUtils.nextInt(0, totalSample) < sampleRate;
    }
    /**
     * judge the path url exists is the targets sets.
     * @param targets need check urls
     * @param path request path
     * @return exist is true and not exist is false
     */
    private boolean containsUrl(Set<String> targets, String path) {
        for (String suffix : targets) {
            if (path.contains(suffix)) {
                return true;
            }
        }
        return false;
    }
}
