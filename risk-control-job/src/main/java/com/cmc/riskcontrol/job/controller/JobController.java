package com.cmc.riskcontrol.job.controller;

import com.cmc.riskcontrol.job.dto.JobAdminDTO;
import com.cmc.riskcontrol.job.service.jobadmin.JobAdminService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/2/14 17:18
 */
@RestController
@RequestMapping("/jobAdmin")
public class JobController {

    @Resource
    private JobAdminService jobAdminService;

    @PostMapping("/execute")
    public ResponseEntity<Boolean> executeJobHandler(@Valid @RequestBody JobAdminDTO jobAdminDTO) {
        return ResponseEntity.ok(jobAdminService.executeJobHandler(jobAdminDTO));
    }
}
