package com.cmc.riskcontrol.job.dto;

/**
 *
 * <AUTHOR>
 * @date 2020/6/1
 */
public class JobHandlingResult<R> {

    private boolean isSucceeded;
    private R data;
    private String errorMsg;

    public JobHandlingResult(boolean isSucceeded) {
        this.isSucceeded = isSucceeded;
    }

    public JobHandlingResult(boolean isSucceeded, R data) {
        this.isSucceeded = isSucceeded;
        this.data = data;
    }

    public JobHandlingResult(boolean isSucceeded, String errorMsg) {
        this.isSucceeded = isSucceeded;
        this.errorMsg = errorMsg;
    }

    /**
     * Whether the job is handled successfully.
     *
     * @return true if it's succeeded.
     */
    public boolean isSucceeded() {
        return this.isSucceeded;
    }

    /**
     * Gets the data returned by the handler as a part of the handling result. It can be data for successful handling or error data when handling
     * is failed.
     *
     * @return the succeeded/failed data
     */
    public R getData() {
        return this.data;
    }

    public void setData(R data) {
        this.data = data;
    }

    /**
     * Returns error message if any.
     *
     * @return the error message
     */
    public String getErrorMsg() {
        return errorMsg;
    }
}
