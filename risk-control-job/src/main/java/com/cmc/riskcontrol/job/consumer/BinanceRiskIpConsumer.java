package com.cmc.riskcontrol.job.consumer;

import com.cmc.framework.kafka.client.ConsumerAfterCommit;
import com.cmc.riskcontrol.job.biz.BinanceRiskIpBiz;
import java.util.Objects;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/02/09
 * @description
 **/
@Slf4j
@Component
public class BinanceRiskIpConsumer  implements ConsumerAfterCommit<String, String> {
    @Autowired
    private BinanceRiskIpBiz binanceRiskIpBiz;
    /**
     * pull records after commit
     *
     * @param record
     */
    @Override
    public void pull(ConsumerRecord<String, String> record) {
        try {
            if (Objects.nonNull(record)){
                binanceRiskIpBiz.handelSingleMsg(record.value());
            }
        } catch (Throwable e) {
            log.error("There was an error in AbnormalIp before commit, record:{}", record, e);
        }
    }
}
