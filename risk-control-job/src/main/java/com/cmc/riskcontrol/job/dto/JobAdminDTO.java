package com.cmc.riskcontrol.job.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* @Description:
* @Author: <PERSON>
* @Date: 2021/7/18
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class JobAdminDTO {
    @NotNull
    private String jobName;
    private String param;
}
