FROM 346764516239.dkr.ecr.us-east-1.amazonaws.com/cmc-maven-base:feature-jdk-upgrade-aeb5ab6d

ARG JAR_FILE
ENV JAR_FILE cmc-risk-control-job.jar
WORKDIR /opt/cmc
ENTRYPOINT java \
            --add-opens java.base/jdk.internal.loader=ALL-UNNAMED \
            --add-opens java.base/jdk.internal.misc=ALL-UNNAMED \
            --add-exports java.base/jdk.internal.ref=ALL-UNNAMED \
            --add-opens java.base/java.io=ALL-UNNAMED \
            --add-opens java.base/java.lang=ALL-UNNAMED \
            --add-opens java.base/java.lang.invoke=ALL-UNNAMED \
            --add-opens java.base/java.lang.reflect=ALL-UNNAMED \
            --add-opens java.base/java.math=ALL-UNNAMED \
            --add-opens java.base/java.net=ALL-UNNAMED \
            --add-opens java.base/java.nio=ALL-UNNAMED \
            --add-opens java.base/java.text=ALL-UNNAMED \
            --add-opens java.base/java.time=ALL-UNNAMED \
            --add-opens java.base/java.util=ALL-UNNAMED \
            --add-opens java.base/java.util.concurrent=ALL-UNNAMED \
            --add-opens java.base/sun.net.www.protocol.https=ALL-UNNAMED \
            --add-opens java.base/sun.nio.ch=ALL-UNNAMED \
            --add-exports jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED \
            --add-opens java.desktop/java.awt.font=ALL-UNNAMED \
            --add-opens java.management/com.sun.jmx.mbeanserver=ALL-UNNAMED \
            --add-opens java.management/com.sun.jmx.interceptor=ALL-UNNAMED \
            --add-opens java.management/sun.management=ALL-UNNAMED \
            --add-exports jdk.unsupported/sun.misc=ALL-UNNAMED \
            $JAVA_OPTS ./${JAR_FILE}
ADD target/${JAR_FILE} /opt/cmc/

