apiVersion: skaffold/v2alpha3
kind: Config
metadata:
  name: botkube
build:
  artifacts:
  - image: 346764516239.dkr.ecr.us-east-1.amazonaws.com/cmc-risk-control-service
    custom:
       buildCommand: mvn -e -V -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dmaven.repo.local=cmc-shop-service/.m2/repository -s settings.xml  -Dmaven.test.skip -Dmdep.skip -Ddocker.skip=true -Ddocker.tag=${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA} clean deploy
  - image: 346764516239.dkr.ecr.us-east-1.amazonaws.com/cmc-risk-control-job
    custom:
       buildCommand: mvn -e -V -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dmaven.repo.local=cmc-shop-job/.m2/repository -s settings.xml  -Dmaven.test.skip -Dmdep.skip -Ddocker.skip=true -Ddocker.tag=${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA} clean deploy
  tagPolicy:
    envTemplate:
      template: "{{.CI_COMMIT_REF_SLUG}}-{{.CI_COMMIT_SHORT_SHA}}"
  local:
    push: true

