<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cmc-risk-control-service</artifactId>
    <packaging>pom</packaging>
    <version>1.9-SNAPSHOT</version>
    <modules>
        <module>risk-control-service</module>
        <module>risk-control-model</module>
        <module>risk-control-business</module>
        <module>risk-control-dao</module>
        <module>risk-control-test-report</module>
        <module>risk-control-job</module>
        <module>risk-control-sdk</module>
    </modules>

    <properties>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <dockerfile-maven.version>1.4.13</dockerfile-maven.version>
        <docker.root.repo>346764516239.dkr.ecr.us-east-1.amazonaws.com</docker.root.repo>
        <docker.tag>latest</docker.tag>
        <docker.skip>true</docker.skip>
        <jacoco.version>0.8.12</jacoco.version>
        <testng.version>6.8</testng.version>
        <mockito.version>5.11.0</mockito.version>
        <mockito-inline.version>5.2.0</mockito-inline.version>
        <aggregate.report.dir>risk-control-test-report/target/site/jacoco-aggregate/jacoco.xml</aggregate.report.dir>
        <sonar.coverage.jacoco.xmlReportPaths>./${aggregate.report.dir}</sonar.coverage.jacoco.xmlReportPaths>
        <cmc.common.version>0.1.0</cmc.common.version>
        <geoip2.version>3.0.0</geoip2.version>
        <s3.version>2.14.7</s3.version>
        <email-sdk.version>1.0.9-SNAPSHOT</email-sdk.version>
        <commons-compress.version>1.21</commons-compress.version>
        <commons.io.version>2.4</commons.io.version>
        <springdoc.version>2.3.0</springdoc.version>
    </properties>

    <parent>
        <groupId>com.cmc</groupId>
        <artifactId>cmc-framework-bom-parent</artifactId>
        <version>2.1.0</version>
        <relativePath />
    </parent>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>cmc-metrics</artifactId>
                <version>${cmc.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>risk-control-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>risk-control-business</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>risk-control-dao</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>email-sdk</artifactId>
                <version>${email-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.maxmind.geoip2</groupId>
                <artifactId>geoip2</artifactId>
                <version>${geoip2.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${s3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>2.30.29</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>${testng.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <scm>
        <connection>scm:git:****************************:cmc-backend/cmc-risk-control-service.git</connection>
        <url>https://git.coinmarketcap.supply/cmc-backend/cmc-risk-control-service</url>
        <tag>1.0.0-SNAPSHOT</tag>
    </scm>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <release>${java.version}</release>
                    <fork>true</fork>
                    <compilerArgs>--enable-preview</compilerArgs>
                    <!--<forceJavacCompilerUse>true</forceJavacCompilerUse>-->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.21.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <inherited>false</inherited>
                        <goals>
                            <goal>aggregate-pmd-check</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <printFailingErrors>true</printFailingErrors>
                    <rulesets>
                        <!--                        <ruleset>rulesets/java/ali-comment.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-concurrent.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-constant.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-exception.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-flowcontrol.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-naming.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-oop.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-orm.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-other.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-set.xml</ruleset>-->
                        <ruleset>rulesets/java/cmc-swagger.xml</ruleset>
                    </rulesets>
                    <failurePriority>2</failurePriority>
                    <verbose>true</verbose>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.alibaba.p3c</groupId>
                        <artifactId>p3c-pmd</artifactId>
                        <version>2.1.1</version>
                    </dependency>
                    <dependency>
                        <groupId>com.cmc</groupId>
                        <artifactId>coding-convention-pmd</artifactId>
                        <version>[1.0.0,)</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>${dockerfile-maven.version}</version>
                    <configuration>
                        <skip>${docker.skip}</skip>
                        <skipDockerInfo>true</skipDockerInfo>
                        <tag>${docker.tag}</tag>
                        <buildArgs>
                            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                        </buildArgs>
                    </configuration>
                    <executions>
                        <execution>
                            <id>default</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                                <goal>push</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.2.5</version>
                    <configuration>
                        <systemPropertyVariables>
                            <jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
                        </systemPropertyVariables>
                        <argLine>@{surefireArgLine} --enable-preview</argLine>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.12</version>
                    <executions>
                        <execution>
                            <id>default-prepare-agent</id>
                            <goals><goal>prepare-agent</goal></goals>
                        </execution>
                    </executions>
                    <configuration>
                        <propertyName>surefireArgLine</propertyName>
                        <excludes>
                            <exclude>com/cmc/**/*Enum.*</exclude>
                            <exclude>com/cmc/**/*Exception.*</exclude>
                            <exclude>com/cmc/**/*Header.*</exclude>
                            <exclude>com/cmc/**/*Request.*</exclude>
                            <exclude>com/cmc/**/*Response.*</exclude>
                            <exclude>com/cmc/**/*DTO.*</exclude>
                            <exclude>com/cmc/**/*Dto.*</exclude>
                            <exclude>com/cmc/**/*PO.*</exclude>
                            <exclude>com/cmc/**/*VO.*</exclude>
                            <exclude>com/cmc/**/*Vo.*</exclude>
                            <exclude>com/cmc/**/*Constants.*</exclude>
                            <exclude>com/cmc/**/config/**</exclude>
                            <exclude>com/cmc/**/*Builder.*</exclude>
                            <exclude>com/cmc/**/*Builder.*</exclude>
                            <exclude>com/cmc/**/*Entity.*</exclude>
                            <exclude>com/cmc/**/*.properties</exclude>
                        </excludes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.21.0</version>
                <reportSets>
                    <reportSet>
                        <id>aggregate</id>
                        <inherited>false</inherited>
                        <reports>
                            <report>aggregate-pmd-no-fork</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>https://nexus.coinmarketcap.supply/repository/maven-releases</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>https://nexus.coinmarketcap.supply/repository/maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>


</project>
