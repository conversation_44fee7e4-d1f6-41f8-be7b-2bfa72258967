<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cmc-risk-control-service</artifactId>
        <groupId>com.cmc</groupId>
        <version>1.9-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>


    <artifactId>risk-control-test-report</artifactId>

    <properties>
        <sonar.coverage.jacoco.xmlReportPaths>${basedir}/../${aggregate.report.dir}</sonar.coverage.jacoco.xmlReportPaths>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.cmc</groupId>
            <artifactId>risk-control-business</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cmc</groupId>
            <artifactId>risk-control-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cmc</groupId>
            <artifactId>risk-control-service</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>aggregate-report</id>
                        <phase>test</phase>
                        <goals><goal>report-aggregate</goal></goals>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <exclude>com/cmc/riskcontrol/service/config/*</exclude>
                        <exclude>com/cmc/riskcontrol/business/aws/*</exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
