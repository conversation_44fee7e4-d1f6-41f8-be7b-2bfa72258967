stages:
  - prerequisites
  - generate_pipeline
  - import_pipeline


include:
  - project: 'cmc-backend/gitlab-scripts'
    ref: tech/pipeline_v3_jkd17
    file: 'Release-maven-project-v3.yml'


variables:
  VERSION_BRANCH: "cmc-backend"
  MODULE_NAME: "cmc-risk-control"
  TEST_REPORT_DIR: risk-control-test-report/target/site/jacoco-aggregate
  ENVS: beta,staging,prod
  SERVICES: service,job
  DEPLOYMENTS: cmc-risk-control-service,cmc-risk-control-job
  APOLLO_IDS: cmc-risk-control-service,cmc-risk-control-job
  IMAGE_POLICY: "standard"
  IMPGE_DOCKERDILE_POLICY: "tpl"
