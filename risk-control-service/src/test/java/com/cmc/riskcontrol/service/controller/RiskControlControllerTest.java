package com.cmc.riskcontrol.service.controller;

import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.riskcontrol.business.event.RiskControlService;
import com.cmc.riskcontrol.model.dto.EventJudgeRequestDto;
import com.cmc.riskcontrol.model.dto.EventJudgeResponseDto;
import com.cmc.riskcontrol.model.dto.EventRecordRequestDto;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.Map;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import io.micrometer.core.instrument.Counter;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class RiskControlControllerTest {

    @Mock
    private RiskControlService mockRiskControlService;
    @Mock
    private CmcMeterRegistry mockCmcMeterRegistry;

    @Mock
    private Counter counter;

    private RiskControlController riskControlControllerUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        riskControlControllerUnderTest = new RiskControlController(mockRiskControlService, mockCmcMeterRegistry);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testRecord() {
        // Setup
        final EventRecordRequestDto eventRecordRequestDto =
            new EventRecordRequestDto("userId", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "email",
                "fvideoId", "clientType", "appVersion", "ua", "ip", "behavior",
                Map.ofEntries(Map.entry("value", "value")));
        when(mockCmcMeterRegistry.counter(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
            .thenReturn(counter);
        when(mockRiskControlService.record(eventRecordRequestDto)).thenReturn(Mono.just(false));

        // Run the test
        final Mono<Boolean> result = riskControlControllerUnderTest.record(eventRecordRequestDto);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(Assert::assertNotNull).verifyComplete();
    }

    @Test
    public void testJudge() {
        // Setup
        final EventJudgeRequestDto eventJudgeRequestDto =
            new EventJudgeRequestDto(0, Map.ofEntries(Map.entry("value", "value")));

        // Configure RiskControlService.judge(...).
        final Mono<EventJudgeResponseDto> eventJudgeResponseDtoMono =
            Mono.just(new EventJudgeResponseDto(false, "riskContent"));
        when(mockRiskControlService.judge(eventJudgeRequestDto)).thenReturn(eventJudgeResponseDtoMono);
        when(mockCmcMeterRegistry.counter(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
            .thenReturn(counter);

        // Run the test
        final Mono<EventJudgeResponseDto> result = riskControlControllerUnderTest.judge(eventJudgeRequestDto);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(Assert::assertNotNull).verifyComplete();

    }

}
