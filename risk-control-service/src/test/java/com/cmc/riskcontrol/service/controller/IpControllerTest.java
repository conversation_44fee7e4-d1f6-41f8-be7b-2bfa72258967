package com.cmc.riskcontrol.service.controller;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.riskcontrol.business.ip.IPService;
import com.cmc.riskcontrol.model.dto.EventRecordRequestDto;
import com.cmc.riskcontrol.model.dto.IPAddressDto;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.Map;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * <AUTHOR>
 * @date 2022/12/18
 * @description
 **/
public class IpControllerTest {
    @Mock
    private  IPService ipService;
    private AutoCloseable mockitoCloseable;
    private IpController ipController;
    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        ipController = new IpController(ipService);
    }
    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }
    @Test
    public void testIpParse() {
        // Setup


        when(ipService.parse(anyString())).thenReturn(IPAddressDto.builder().build());

        // Run the test
        final Mono<IPAddressDto> result = ipController.parseIp("127.0.0.1");

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(Assert::assertNotNull).verifyComplete();
    }

}
