package com.cmc.riskcontrol.business.ip;

import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertEquals;

import com.cmc.riskcontrol.business.aws.AwsS3Service;
import com.cmc.riskcontrol.model.dto.IPAddressDto;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.core.io.ResourceLoader;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class IPServiceTest {

    @Mock
    private AwsS3Service mockAwsS3Service;
    @Mock
    private ResourceLoader mockResourceLoader;

    @InjectMocks
    private IPService ipServiceUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testParse() {
        // Setup
        final IPAddressDto expectedResult = new IPAddressDto("ip", null, null, null);

        // Run the test
        final IPAddressDto result = ipServiceUnderTest.parse("ip");

        // Verify the results
        assertEquals(expectedResult, result);
    }

}
