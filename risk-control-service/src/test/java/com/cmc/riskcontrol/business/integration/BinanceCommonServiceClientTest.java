package com.cmc.riskcontrol.business.integration;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.framework.common.http.HttpWebClient;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.riskcontrol.model.rmi.CommonRuleRequestDto;
import com.cmc.riskcontrol.model.rmi.CommonRuleRequestDto.CommonRuleBody;
import com.cmc.riskcontrol.model.rmi.CommonRuleResponseDto;
import java.util.Map;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class BinanceCommonServiceClientTest {

    @Mock
    protected HttpWebClient client;

    @InjectMocks
    private BinanceCommonServiceClient binanceCommonServiceClient;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(binanceCommonServiceClient, "url", "");
        ReflectionTestUtils.setField(binanceCommonServiceClient, "clientId", "");
        ReflectionTestUtils.setField(binanceCommonServiceClient, "accessToken", "");
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testCommonRule() {
        // Setup
        final CommonRuleRequestDto ruleRequestDto =
            new CommonRuleRequestDto(new CommonRuleBody("eventCode", Map.ofEntries(Map.entry("value", "value"))));
        final CommonRuleResponseDto commonRuleResponseDto = new CommonRuleResponseDto(false, null, null);

        Mono<ResponseEntity<String>> response =
            Mono.just(ResponseEntity.ok(JacksonUtils.serialize(commonRuleResponseDto)));
        when(client.post(Mockito.anyString(), Mockito.any(), any())).thenReturn(response);
        // Run the test
        final Mono<CommonRuleResponseDto> result = binanceCommonServiceClient.commonRule(ruleRequestDto);

        // Verify the result
        StepVerifier.create(result).expectSubscription().assertNext(Assert::assertNotNull).verifyComplete();
    }
}
