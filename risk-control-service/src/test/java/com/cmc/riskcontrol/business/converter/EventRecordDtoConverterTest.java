package com.cmc.riskcontrol.business.converter;

import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertEquals;

import com.cmc.riskcontrol.business.ip.IPService;
import com.cmc.riskcontrol.model.dto.EventRecordRequestDto;
import com.cmc.riskcontrol.model.dto.IPAddressDto;
import com.cmc.riskcontrol.model.kafka.EventMessage;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.Map;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class EventRecordDtoConverterTest {

    @Mock
    private IPService mockIpService;

    @InjectMocks
    private EventRecordDtoConverter eventRecordDtoConverterUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testConvert() {
        // Setup
        final EventRecordRequestDto input =
            new EventRecordRequestDto("userId", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "email",
                "fvideoId", "clientType", "appVersion", "ua", "ip", "behavior",
                Map.ofEntries(Map.entry("value", "value")));
        final EventMessage expectedResult =
            new EventMessage("userId", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "email", "fvideoId",
                "clientType", "appVersion", "ua", "ip", "ipCountry", "ipRegion", "ipCity", "behavior",
                Map.ofEntries(Map.entry("value", "value")));

        // Configure IPService.parse(...).
        final IPAddressDto ipAddressDto = new IPAddressDto("ip", "ipCountry", "ipRegion", "ipCity");
        when(mockIpService.parse("ip")).thenReturn(ipAddressDto);

        // Run the test
        final EventMessage result = eventRecordDtoConverterUnderTest.convert(input);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
