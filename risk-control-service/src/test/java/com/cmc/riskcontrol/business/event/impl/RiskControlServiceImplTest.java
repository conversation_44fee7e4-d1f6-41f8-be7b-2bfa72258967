package com.cmc.riskcontrol.business.event.impl;

import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.riskcontrol.business.converter.EventRecordDtoConverter;
import com.cmc.riskcontrol.business.integration.BinanceCommonServiceClient;
import com.cmc.riskcontrol.business.message.EventMessageSendService;
import com.cmc.riskcontrol.model.dto.EventJudgeRequestDto;
import com.cmc.riskcontrol.model.dto.EventJudgeResponseDto;
import com.cmc.riskcontrol.model.dto.EventRecordRequestDto;
import com.cmc.riskcontrol.model.kafka.EventMessage;
import com.cmc.riskcontrol.model.kafka.PushMessageEvent;
import com.cmc.riskcontrol.model.rmi.CommonRuleRequestDto;
import com.cmc.riskcontrol.model.rmi.CommonRuleResponseDto;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.Map;
import org.junit.Assert;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class RiskControlServiceImplTest {

    @Mock
    private BinanceCommonServiceClient mockBinanceCommonServiceClient;
    @Mock
    private EventMessageSendService mockEventMessageSendService;
    @Mock
    private EventRecordDtoConverter mockEventRecordDtoConverter;
    @Mock
    private CmcMeterRegistry mockCmcMeterRegistry;

    private RiskControlServiceImpl riskControlServiceImplUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        riskControlServiceImplUnderTest =
            new RiskControlServiceImpl(mockBinanceCommonServiceClient, mockEventMessageSendService, mockEventRecordDtoConverter,
                mockCmcMeterRegistry);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testRecord() {
        // Setup
        final EventRecordRequestDto eventRecordRequestDto =
            new EventRecordRequestDto("userId", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "email", "fvideoId",
                "clientType", "appVersion", "ua", "ip", "behavior", Map.ofEntries(Map.entry("value", "value")));

        // Configure EventRecordDtoConverter.convert(...).
        final EventMessage eventMessage =
            new EventMessage("userId", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "email", "fvideoId", "clientType",
                "appVersion", "ua", "ip", "ipCountry", "ipRegion", "ipCity", "behavior", Map.ofEntries(Map.entry("value", "value")));
        when(mockEventRecordDtoConverter.convert(
            new EventRecordRequestDto("userId", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "email", "fvideoId",
                "clientType", "appVersion", "ua", "ip", "behavior", Map.ofEntries(Map.entry("value", "value"))))).thenReturn(eventMessage);

        when(mockEventMessageSendService.send(Mockito.any(PushMessageEvent.class))).thenReturn(Mono.just(true));

        // Run the test
        final Mono<Boolean> result = riskControlServiceImplUnderTest.record(eventRecordRequestDto);

        // Verify the results
        StepVerifier.create(result).expectNext(true).verifyComplete();
    }

    @Test
    public void testJudge() {
        // Setup
        final EventJudgeRequestDto eventJudgeRequestDto = new EventJudgeRequestDto(0, Map.ofEntries(Map.entry("value", "value")));

        // Configure BinanceCommonServiceClient.commonRule(...).
        final Mono<CommonRuleResponseDto> commonRuleResponseDtoMono = Mono.just(
            new CommonRuleResponseDto(false, Map.ofEntries(Map.entry("value", "value")), Map.ofEntries(Map.entry("value", false))));
        when(mockBinanceCommonServiceClient.commonRule(Mockito.any(CommonRuleRequestDto.class))).thenReturn(commonRuleResponseDtoMono);

        when(mockCmcMeterRegistry.counter("name", "tags")).thenReturn(null);

        // Run the test
        final Mono<EventJudgeResponseDto> result = riskControlServiceImplUnderTest.judge(eventJudgeRequestDto);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(res -> {
            System.out.println(res);
            Assert.assertNotNull(res);
        }).verifyComplete();
    }

}
