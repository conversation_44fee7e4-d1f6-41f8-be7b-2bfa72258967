package com.cmc.riskcontrol.business.message.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.framework.kafka.client.KafkaProducer;
import com.cmc.riskcontrol.model.kafka.PushMessageEvent;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class EventMessageSendServiceImplTest {

    @Mock
    private KafkaProducer mockKafkaProducer;

    @InjectMocks
    private EventMessageSendServiceImpl eventMessageSendServiceImplUnderTest;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testSend() {
        // Setup
        final PushMessageEvent messageEvent = new PushMessageEvent("kafkaTopicName", "message");
        when(mockKafkaProducer.send(anyString(), any(Mono.class))).thenReturn(Mono.just(Mono.empty()));

        // Run the test
        final Mono<Boolean> result = eventMessageSendServiceImplUnderTest.send(messageEvent);

        // Verify the results
        StepVerifier.create(result).expectSubscription().assertNext(Assert::assertNotNull).verifyComplete();
    }

}
