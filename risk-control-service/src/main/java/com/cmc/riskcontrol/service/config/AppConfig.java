package com.cmc.riskcontrol.service.config;

import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.config.EnableWebFlux;
import org.springframework.web.reactive.config.WebFluxConfigurer;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;

/**
 * cmc-api
 *
 * <AUTHOR>
 * @date 2022-01-13 15:06
 */
@Configuration
@EnableWebFlux
public class AppConfig implements WebFluxConfigurer {

    @Bean
    public OpenAPI customOpenApi() {
        return new OpenAPI()
                .info(new Info()
                        .title("cmc risk control service")
                        .version("3.0.0")
                        .contact(new Contact()
                                .name("<PERSON>")
                                .email("<EMAIL>"))
                        .description("API 文档"));
    }

    @Bean
    public GroupedOpenApi cmcRiskControlApi() {
        return GroupedOpenApi.builder()
                .group("cmc-riskcontrol-service") // 分组名称
                .packagesToScan("com.cmc.riskcontrol.service.controller")
                .pathsToMatch("/**")
                .build();
    }

}
