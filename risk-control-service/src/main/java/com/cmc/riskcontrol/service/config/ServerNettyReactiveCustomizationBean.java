package com.cmc.riskcontrol.service.config;

import static io.netty.channel.ChannelOption.CONNECT_TIMEOUT_MILLIS;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.autoconfigure.web.reactive.ReactiveWebServerFactoryCustomizer;
import org.springframework.boot.web.embedded.netty.NettyReactiveWebServerFactory;
import org.springframework.boot.web.reactive.server.ConfigurableReactiveWebServerFactory;
import org.springframework.stereotype.Component;
import reactor.netty.resources.LoopResources;

/**
 * <AUTHOR>
 * @date 2022-01-13 15:06
 * @description
 */
@Component
public class ServerNettyReactiveCustomizationBean extends ReactiveWebServerFactoryCustomizer {

    @Value("${com.cmc.riskcontrol.io-worker-count:128}")
    private Integer ioWorkerCount;

    public ServerNettyReactiveCustomizationBean(ServerProperties serverProperties) {
        super(serverProperties);
    }

    @Override
    public void customize(ConfigurableReactiveWebServerFactory factory) {
        super.customize(factory);
        NettyReactiveWebServerFactory nettyFactory = (NettyReactiveWebServerFactory)factory;
        nettyFactory.setResourceFactory(null);
        nettyFactory.addServerCustomizers(httpServer ->
                httpServer.runOn(LoopResources.create("cmc-server-loop", 2, ioWorkerCount, true))
                        .option(CONNECT_TIMEOUT_MILLIS, 30000)
        );
    }
}
