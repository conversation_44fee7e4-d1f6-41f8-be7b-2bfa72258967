package com.cmc.riskcontrol.service.controller;

import com.cmc.framework.common.ApiResponse;
import com.cmc.riskcontrol.business.url.UrlService;
import com.cmc.riskcontrol.model.dto.UrlValidationRequestDto;
import com.cmc.riskcontrol.model.dto.UrlValidationResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/7/28 10:22
 */
@Tag(name = "UrlController", description = "Url related APIs")
@RestController
@ApiResponse
@RequestMapping("/system/v4/url")
public class UrlController {

    @Autowired
    private UrlService urlService;

    @Operation(summary = "Validate url", description = "Validate url")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "url validation result")
    })
    @PostMapping("/validate")
    public Mono<UrlValidationResponseDto> validateUrl(
        @Parameter(description = "url", required = true)
        @RequestBody UrlValidationRequestDto requestDto) {
        return urlService.validateUrl(requestDto);
    }
}
