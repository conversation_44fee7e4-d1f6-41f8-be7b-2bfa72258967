package com.cmc.riskcontrol.service.controller;

import com.cmc.framework.common.ApiResponse;
import com.cmc.riskcontrol.business.ip.IPService;
import com.cmc.riskcontrol.model.dto.IPAddressDto;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2022/12/12
 * @description
 **/
@Tag(name = "IpController", description = "ip related APIs")
@RestController
@RequestMapping("/v4/system/ip")
@ApiResponse
@Validated
public class IpController {
    private final IPService ipService;

    public IpController(IPService ipService) {
        this.ipService = ipService;
    }
    @GetMapping("/parse/{ip}")
    public Mono<IPAddressDto> parseIp(@PathVariable String ip){
        return Mono.just(ipService.parse(ip));
    }

    @GetMapping(value = "/geodata")
    public ResponseEntity<Flux<DataBuffer>> getGeoDataFromLocal() {
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + IPService.MMDB_FILENAME)
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .body(ipService.getGeoData());
    }

    @GetMapping(value = "/refresh-ratelimit")
    public void refreshRateLimit() {
        ipService.refreshRateLimit();
    }
}
