package com.cmc.riskcontrol.service;

import com.cmc.auth.common.utils.AuthUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;

/**
 * <AUTHOR>
 * @date 2022-01-13 15:06
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.cmc.riskcontrol.*", "com.cmc.email.sdk.*"}, excludeFilters = {
    @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = AuthUtils.class)})
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class RiskControlServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(RiskControlServiceApplication.class, args);
    }
}
