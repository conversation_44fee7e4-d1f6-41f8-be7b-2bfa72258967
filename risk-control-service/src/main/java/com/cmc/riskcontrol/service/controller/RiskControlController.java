package com.cmc.riskcontrol.service.controller;

import com.cmc.framework.common.ApiResponse;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.riskcontrol.business.event.RiskControlService;
import com.cmc.riskcontrol.model.constant.MetricConstant;
import com.cmc.riskcontrol.model.dto.EventJudgeRequestDto;
import com.cmc.riskcontrol.model.dto.EventJudgeResponseDto;
import com.cmc.riskcontrol.model.dto.EventRecordRequestDto;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2022/1/17 11:37
 */
@Tag(name = "RiskControlController", description = "risk control APIs")
@RestController
@RequestMapping("/v4/system/rc")
@ApiResponse
@Validated
public class RiskControlController {

    private final RiskControlService riskControlService;

    private final CmcMeterRegistry cmcMeterRegistry;

    public RiskControlController(RiskControlService riskControlService, CmcMeterRegistry cmcMeterRegistry) {
        this.riskControlService = riskControlService;
        this.cmcMeterRegistry = cmcMeterRegistry;
    }

    @PostMapping("/record")
    @Operation(summary = "record event by risk control service")
    public Mono<Boolean> record(@RequestBody @Valid EventRecordRequestDto eventRecordRequestDto) {
        cmcMeterRegistry.counter(MetricConstant.RISK_CONTROL_API_COUNT, "api", "record").increment();
        return riskControlService.record(eventRecordRequestDto);
    }

    @PostMapping("/judge")
    @Operation(summary = "judge event by risk control service")
    public Mono<EventJudgeResponseDto> judge(@RequestBody @Valid EventJudgeRequestDto eventJudgeRequestDto) {
        cmcMeterRegistry.counter(MetricConstant.RISK_CONTROL_API_COUNT, "api", "judge").increment();
        return riskControlService.judge(eventJudgeRequestDto);
    }

}
