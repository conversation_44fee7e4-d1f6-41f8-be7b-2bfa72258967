server.env=prod

#auth
auth.api.base.url=http://cmc-auth-service-service:8100/

#kafka
cmc.boot.kafka.settings.event.bootstrapServers=kafka-new1.prod1fdg.net:9092 , kafka-new2.prod1fdg.net:9092, kafka-new3.prod1fdg.net:9092
##kafka producer config
### topic - risk_control_record_event_prod
cmc.boot.kafka.settings.event.producer.risk_control_record_event_prod.ack=all
cmc.boot.kafka.settings.event.producer.risk_control_record_event_prod.keySerializer=org.apache.kafka.common.serialization.StringSerializer
cmc.boot.kafka.settings.event.producer.risk_control_record_event_prod.valueSerializer=org.apache.kafka.common.serialization.StringSerializer
cmc.boot.kafka.settings.event.producer.risk_control_record_event_prod.requestTimeout=60000
cmc.boot.kafka.settings.event.producer.risk_control_record_event_prod.compression=lz4

#topics
com.cmc.riskcontrol.event.record.topic=risk_control_record_event_prod

com.cmc.riskcontrol.client.binance-api=https://api.commonservice.io
com.cmc.riskcontrol.client.binance-api.clientId=cmc
com.cmc.riskcontrol.client.binance-api.accessToken=Ub8T6LmX9JLD1Vic

# Amazon s3
aws.s3.bucket = s2.coinmarketcap.com

com.cmc.riskcontrol.aws.region = us-east-1
com.cmc.riskcontrol.aws.access-key-id =
com.cmc.riskcontrol.aws.secret-access-key =
com.cmc.riskcontrol.aws.s3.bucket = s3.coinmarketcap.com
