server.env=staging

#auth
auth.api.base.url=http://cmc-auth-service-service:8100/

#kafka
cmc.boot.kafka.settings.event.bootstrapServers=172.21.43.159:9092,172.21.43.133:9092,172.21.43.45:2181:9092
##kafka producer config
### topic - risk_control_record_event_dev
cmc.boot.kafka.settings.event.producer.risk_control_record_event_dev.ack=all
cmc.boot.kafka.settings.event.producer.risk_control_record_event_dev.keySerializer=org.apache.kafka.common.serialization.StringSerializer
cmc.boot.kafka.settings.event.producer.risk_control_record_event_dev.valueSerializer=org.apache.kafka.common.serialization.StringSerializer
cmc.boot.kafka.settings.event.producer.risk_control_record_event_dev.requestTimeout=60000
cmc.boot.kafka.settings.event.producer.risk_control_record_event_dev.compression=lz4

#topics
com.cmc.riskcontrol.event.record.topic=risk_control_record_event_beta

com.cmc.riskcontrol.client.binance-api=https://r.devfdg.net
com.cmc.riskcontrol.client.binance-api.clientId=cmc
com.cmc.riskcontrol.client.binance-api.accessToken=GeqWAfgxBweBBp6B

# Amazon s3
aws.s3.bucket = testingstatic.coinmarketcap.com

com.cmc.riskcontrol.aws.region = us-east-1
com.cmc.riskcontrol.aws.access-key-id =
com.cmc.riskcontrol.aws.secret-access-key =
com.cmc.riskcontrol.aws.s3.bucket = s3.staging.coinmarketcap.com
