server.env=beta

# Apollo
app.id = cmc-risk-control-service
apollo.bootstrap.enabled = true
apollo.bootstrap.namespaces = application,backend.common,backend.influxdb

#auth
auth.api.base.url= https://api.beta.coinmarketcap.supply/auth

#kafka
cmc.boot.kafka.settings.event.bootstrapServers=kafka-new1.qa1fdg.net:9092,kafka-new2.qa1fdg.net:9092,kafka-new3.qa1fdg.net:9092
##kafka producer config
### topic - risk_control_record_event_beta
cmc.boot.kafka.settings.event.producer.risk_control_record_event_beta.ack=all
cmc.boot.kafka.settings.event.producer.risk_control_record_event_beta.keySerializer=org.apache.kafka.common.serialization.StringSerializer
cmc.boot.kafka.settings.event.producer.risk_control_record_event_beta.valueSerializer=org.apache.kafka.common.serialization.StringSerializer
cmc.boot.kafka.settings.event.producer.risk_control_record_event_beta.requestTimeout=60000
cmc.boot.kafka.settings.event.producer.risk_control_record_event_beta.compression=lz4

#topics
com.cmc.riskcontrol.event.record.topic=risk_control_record_event_beta

com.cmc.riskcontrol.client.binance-api=https://r.qa1fdg.net
com.cmc.riskcontrol.client.binance-api.clientId=cmc
com.cmc.riskcontrol.client.binance-api.accessToken=BuokhxYJrwSPYKMS

# Amazon s3
aws.s3.bucket = testingstatic.coinmarketcap.com

com.cmc.riskcontrol.aws.region = us-east-1
com.cmc.riskcontrol.aws.access-key-id =
com.cmc.riskcontrol.aws.secret-access-key =
com.cmc.riskcontrol.aws.s3.bucket = s3.beta.coinmarketcap.com


