server.env=dev

server.compression.enabled = true
server.compression.mime-types = text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size = 1024
server.http2.enabled = true
server.port = 8800
server.servlet.context-path = /
spring.application.name = cmc-risk-control-service

management.endpoint.prometheus.enabled = true
management.endpoints.web.exposure.include = info,env,prometheus
management.health.redis.enabled = false
management.metrics.tags.appId = ${spring.application.name}
#management.metrics.tags.application=${spring.application.name}

spring.autoconfigure.exclude = org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration

# Need by Apollo
app.id = cmc-risk-control-service
apollo.bootstrap.enabled = true
apollo.bootstrap.namespaces = application,backend.common,backend.influxdb

#kafka
cmc.boot.kafka.settings.event.bootstrapServers = kafka-new1.qa1fdg.net:9092,kafka-new2.qa1fdg.net:9092,kafka-new3.qa1fdg.net:9092
##kafka producer config
### topic - risk_control_record_event_beta
cmc.boot.kafka.settings.event.producer.risk_control_record_event_beta.ack = all
cmc.boot.kafka.settings.event.producer.risk_control_record_event_beta.keySerializer = org.apache.kafka.common.serialization.StringSerializer
cmc.boot.kafka.settings.event.producer.risk_control_record_event_beta.valueSerializer = org.apache.kafka.common.serialization.StringSerializer
cmc.boot.kafka.settings.event.producer.risk_control_record_event_beta.requestTimeout = 60000
cmc.boot.kafka.settings.event.producer.risk_control_record_event_beta.compression = lz4

#topics
com.cmc.riskcontrol.event.record.topic = risk_control_record_event_beta

com.cmc.riskcontrol.geoip.file.license-key = ${geoip_key}

# Amazon s3
aws.s3.aws.region = us-east-1
aws.s3.aws.access.key.id =
aws.s3.aws.secret.access.key =

jwt.auth.secret=

# file
com.cmc.riskcontrol.file.localPath = /tmp
com.cmc.riskcontrol.file.mmdb.s3Path = ip/GeoIP2-City.mmdb
com.cmc.riskcontrol.file.mmdb.refreshInterval = 168

com.cmc.riskcontrol.client.binance-api = https://accounts.sdtaop.com
com.cmc.riskcontrol.client.binance-api.clientId = cmc
com.cmc.riskcontrol.client.binance-api.accessToken = BuokhxYJrwSPYKMS
com.cmc.email.send-topic = unknow-topic
com.cmc.riskcontrol.url.custom-patterns = https://3tx5l0a41pyt5hvqk0tz7lllxc33r5fu.oastify.com,http://pudgypenguins.web3interface.io,test1234,oastify.com,ngrok

