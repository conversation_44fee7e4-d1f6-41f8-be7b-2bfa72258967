spring.profiles.active=beta
spring.resources.chain.html-application-cache=true
spring.resources.chain.enabled=true
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024
server.http2.enabled=true
server.port=8800
server.env=beta
server.servlet.context-path=/
spring.application.name=cmc-risk-control-service

com.cmc.riskcontrol.geoip.file.license-key=LB4rYY8L5qYSCfaB

# Amazon s3
aws.s3.aws.region=us-east-1
aws.s3.aws.access.key.id=
aws.s3.aws.secret.access.key=

# file
com.cmc.riskcontrol.file.localPath=/tmp
com.cmc.riskcontrol.file.mmdb.s3-path=ip/GeoIP2-City.mmdb
com.cmc.riskcontrol.file.mmdb.refresh-interval=168

management.endpoint.prometheus.enabled=true
management.endpoints.web.exposure.include=info,prometheus,health

spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
server.forward-headers-strategy=framework