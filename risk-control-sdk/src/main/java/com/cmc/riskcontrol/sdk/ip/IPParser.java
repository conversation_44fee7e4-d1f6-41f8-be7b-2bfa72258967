package com.cmc.riskcontrol.sdk.ip;

import com.cmc.riskcontrol.model.dto.IPAddressDto;
import com.maxmind.db.CHMCache;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.City;
import com.maxmind.geoip2.record.Country;
import com.maxmind.geoip2.record.Subdivision;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.URL;
import java.net.URLConnection;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

/**
 * <AUTHOR>
 * @since 2022/02/17
 */
public final class IPParser {

    private static final Logger LOG = LoggerFactory.getLogger(IPParser.class);

    private static final String LATEST_DOWNLOAD_PATH = "src/main/resources/mmdb/latest/GeoIP2-City.mmdb";

    private static final ClassPathResource BACKUP_RESOURCE = new ClassPathResource("mmdb/backup/GeoIP2-City.mmdb");

    /**
     * the download cycle between 144 ~ 288 hours, which means 6 ~ 12 days
     */
    private static final Long REFRESH_INTERVAL = RandomUtils.nextLong(144, 288);

    private static final AtomicReference<String> DOWNLOAD_URL = new AtomicReference<>();

    private static DatabaseReader READER = null;

    public IPParser() {
    }

    public static void setHost(String host) {
        String downloadUrl = String.format("%s/v4/system/ip/geodata", host);
        DOWNLOAD_URL.set(downloadUrl);
    }

    public static IPAddressDto parse(String ip) {
        try {
            InetAddress ipAddress = InetAddress.getByName(ip);
            CityResponse response = READER.city(ipAddress);
            Country country = response.getCountry();
            Subdivision subdivision = response.getMostSpecificSubdivision();
            City city = response.getCity();

            return IPAddressDto.builder()
                .ip(ip)
                .ipCountry(country.getIsoCode())
                .ipRegion(subdivision.getName())
                .ipCity(city.getName())
                .build();
        } catch (Exception e) {
            return IPAddressDto.builder().ip(ip).build();
        }
    }

    public static Set<String> parseLocation(String ip) {
        try {
            Set<String> locationInfo = new LinkedHashSet<>();
            InetAddress ipAddress = InetAddress.getByName(ip);
            CityResponse response = READER.city(ipAddress);
            Country country = response.getCountry();
            Subdivision subdivision = response.getMostSpecificSubdivision();
            City city = response.getCity();

            locationInfo.add(country.getName());
            locationInfo.add(subdivision.getName());
            locationInfo.add(city.getName());
            return locationInfo;

        } catch (Exception e) {
            return Set.of();
        }

    }

    protected static void refresh() {
        try {
            File file = downloadFileFromRiskControlService();
            if (file.length() == 0) {
                READER = new DatabaseReader.Builder(BACKUP_RESOURCE.getInputStream()).withCache(new CHMCache()).build();
            } else {
                READER = new DatabaseReader.Builder(file).withCache(new CHMCache()).build();
            }
        } catch (IOException e) {
            LOG.error("refresh fail", e);
        }
    }

    private static void updateMmdbFileScheduled() {
        ScheduledExecutorService service = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r);
            t.setName("update-mmdb-thread");
            t.setDaemon(true);
            return t;
        });
        service.scheduleWithFixedDelay(IPParser::refresh, 1, REFRESH_INTERVAL, TimeUnit.HOURS);
    }

    public static File downloadFileFromRiskControlService() throws IOException {
        File file = new File(LATEST_DOWNLOAD_PATH);
        if (file.exists()) {
            file.delete();
        }

        if (DOWNLOAD_URL.get() == null) {
            return file;
        }

        try {
            URLConnection urlConnection = new URL(DOWNLOAD_URL.get()).openConnection();
            saveFile(urlConnection, LATEST_DOWNLOAD_PATH);
        } catch (Exception e) {
            LOG.error("save mmdb file from risk service fail, downloadUrl:{}, downloadPath:{}",
                DOWNLOAD_URL.get(),
                LATEST_DOWNLOAD_PATH,
                e);
        }
        return file;
    }

    public static void saveFile(URLConnection urlConnection, String fileName) throws IOException {
        try (InputStream inputStream = urlConnection.getInputStream();
            FileOutputStream outputStream = new FileOutputStream(fileName)) {
            IOUtils.copy(inputStream, outputStream);
        }
    }

    static {
        refresh();
        updateMmdbFileScheduled();
    }
}
