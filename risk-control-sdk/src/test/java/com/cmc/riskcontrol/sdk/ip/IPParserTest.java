package com.cmc.riskcontrol.sdk.ip;

import com.cmc.riskcontrol.model.dto.IPAddressDto;
import java.io.IOException;
import java.util.Set;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2022/02/18
 */
public class IPParserTest {

    @Test
    public void testParse() throws IOException, InterruptedException {
        // Setup
        // host = "https://api.beta.coinmarketcap.supply/risk-control"
        IPParser.setHost("invalid host");

        // Run the test
        IPAddressDto ipAddressDto = IPParser.parse("***************");

        // Verify the results
        Assert.assertEquals(ipAddressDto, IPAddressDto.builder()
                .ip("***************")
                .ipCountry("CA")
                .ipRegion("Ontario")
                .ipCity("Toronto")
                .build());

        // Setup
        Thread.sleep(200);
        IPParser.refresh();

        // Run the test
        ipAddressDto = IPParser.parse("**************");

        // Verify the results
        Assert.assertEquals(ipAddressDto, IPAddressDto.builder()
                .ip("**************")
                .ipCountry("US")
                .ipRegion("California")
                .ipCity("Pacifica")
                .build());
    }

    @Test
    public void testParseLocation() throws IOException, InterruptedException {
        // Setup
        // host = "https://api.beta.coinmarketcap.supply/risk-control"
        IPParser.setHost("invalid host");

        // Run the test
        Set<String> geo = IPParser.parseLocation("***************");

        // Verify the results
        Assert.assertEquals(geo, Set.of("Canada", "Ontario", "Toronto"));

        // Setup
        Thread.sleep(200);
        IPParser.refresh();

        // Run the test
        geo = IPParser.parseLocation("**************");

        // Verify the results
        Assert.assertEquals(geo, Set.of("United States", "Pacifica", "California"));
    }
}
